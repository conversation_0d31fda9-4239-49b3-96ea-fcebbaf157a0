const axios = require('axios');
const FormData = require('form-data');

// IPFS API 基础URL
const IPFS_API_BASE = 'http://127.0.0.1:5001/api/v0';

// 创建axios实例
const ipfsApi = axios.create({
    baseURL: IPFS_API_BASE,
    timeout: 10000
});

async function testIPFSConnection() {
    console.log('🚀 IPFS 连通性测试工具');
    console.log('====================\n');
    console.log('开始测试 IPFS 连接...\n');
    
    try {
        // 1. 测试节点ID和基本信息
        console.log('1. 获取节点信息...');
        const idResponse = await ipfsApi.post('/id');
        const nodeInfo = idResponse.data;
        console.log('✅ 成功连接到 IPFS 节点!');
        console.log(`   节点ID: ${nodeInfo.ID}`);
        console.log(`   代理版本: ${nodeInfo.AgentVersion}`);
        console.log(`   协议版本: ${nodeInfo.ProtocolVersion}`);
        console.log(`   地址数量: ${nodeInfo.Addresses ? nodeInfo.Addresses.length : 0}`);
        console.log('');

        // 2. 测试版本信息
        console.log('2. 获取版本信息...');
        const versionResponse = await ipfsApi.post('/version');
        const versionInfo = versionResponse.data;
        console.log(`✅ IPFS 版本: ${versionInfo.Version}`);
        console.log(`   提交哈希: ${versionInfo.Commit}`);
        console.log(`   Go版本: ${versionInfo.Golang}`);
        console.log('');

        // 3. 测试添加文件
        console.log('3. 测试文件添加...');
        const testContent = 'Hello IPFS! 这是一个测试文件。';
        const formData = new FormData();
        formData.append('file', Buffer.from(testContent), {
            filename: 'test.txt',
            contentType: 'text/plain'
        });
        
        const addResponse = await ipfsApi.post('/add', formData, {
            headers: {
                ...formData.getHeaders()
            }
        });
        
        const addResult = addResponse.data;
        const fileHash = addResult.Hash;
        console.log(`✅ 文件添加成功!`);
        console.log(`   文件哈希: ${fileHash}`);
        console.log(`   文件大小: ${addResult.Size} bytes`);
        console.log('');

        // 4. 测试读取文件
        console.log('4. 测试文件读取...');
        const catResponse = await ipfsApi.post(`/cat?arg=${fileHash}`);
        const retrievedContent = catResponse.data;
        console.log(`✅ 文件读取成功!`);
        console.log(`   读取内容: "${retrievedContent}"`);
        console.log(`   内容匹配: ${retrievedContent === testContent ? '✅' : '❌'}`);
        console.log('');

        // 5. 测试节点状态
        console.log('5. 检查节点状态...');
        const statsResponse = await ipfsApi.post('/repo/stat');
        const stats = statsResponse.data;
        console.log(`✅ 仓库统计信息:`);
        console.log(`   仓库大小: ${(stats.RepoSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`   存储限制: ${(stats.StorageMax / 1024 / 1024).toFixed(2)} MB`);
        console.log(`   对象数量: ${stats.NumObjects}`);
        console.log('');

        // 6. 测试网络连接
        console.log('6. 检查网络连接...');
        try {
            const peersResponse = await ipfsApi.post('/swarm/peers');
            const peers = peersResponse.data.Peers || [];
            console.log(`✅ 网络连接状态:`);
            console.log(`   连接的节点数: ${peers.length}`);
            if (peers.length > 0) {
                console.log(`   示例连接节点: ${peers[0].Peer}`);
            }
        } catch (peerError) {
            console.log(`⚠️  网络连接检查失败: ${peerError.message}`);
        }
        console.log('');

        // 7. 测试固定文件
        console.log('7. 测试文件固定...');
        try {
            await ipfsApi.post(`/pin/add?arg=${fileHash}`);
            console.log(`✅ 文件固定成功: ${fileHash}`);
        } catch (pinError) {
            console.log(`⚠️  文件固定失败: ${pinError.message}`);
        }
        console.log('');

        console.log('🎉 所有测试完成! IPFS 节点运行正常。');
        console.log('\n📊 测试摘要:');
        console.log(`   - 节点ID: ${nodeInfo.ID.substring(0, 12)}...`);
        console.log(`   - IPFS版本: ${versionInfo.Version}`);
        console.log(`   - 测试文件哈希: ${fileHash}`);
        console.log(`   - 仓库大小: ${(stats.RepoSize / 1024 / 1024).toFixed(2)} MB`);

    } catch (error) {
        console.error('❌ IPFS 连接测试失败:');
        console.error(`   错误类型: ${error.name}`);
        console.error(`   错误信息: ${error.message}`);
        
        if (error.code === 'ECONNREFUSED') {
            console.error('\n💡 建议检查:');
            console.error('   1. IPFS 守护进程是否正在运行 (ipfs daemon)');
            console.error('   2. API 端口是否为 5001');
            console.error('   3. 防火墙设置是否阻止了连接');
            console.error('   4. 运行命令: ipfs config Addresses.API');
        } else if (error.response) {
            console.error(`   HTTP状态码: ${error.response.status}`);
            console.error(`   响应数据: ${JSON.stringify(error.response.data, null, 2)}`);
        }
        
        process.exit(1);
    }
}

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('\n\n👋 测试被中断，正在退出...');
    process.exit(0);
});

// 调用函数进行测试
testIPFSConnection();
