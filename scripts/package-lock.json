{"name": "scripts", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"@helia/http": "^2.1.2", "@helia/unixfs": "^5.0.3", "axios": "^1.9.0", "ipfs-http-client": "^56.0.3"}}, "node_modules/@assemblyscript/loader": {"version": "0.9.4", "resolved": "https://registry.npmjs.org/@assemblyscript/loader/-/loader-0.9.4.tgz", "integrity": "sha512-HazVq9zwTVwGmqdwYzu7WyQ6FQVZ7SwET0KKQuKm55jD0IfUpZgN0OPIiZG3zV1iSrVYcN0bdwLRXI/VNCYsUA=="}, "node_modules/@chainsafe/is-ip": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@chainsafe/is-ip/-/is-ip-2.1.0.tgz", "integrity": "sha512-KIjt+6IfysQ4GCv66xihEitBjvhU/bixbbbFxdJ1sqCp4uJ0wuZiYBPhksZoy4lfaF0k9cwNzY5upEW/VWdw3w=="}, "node_modules/@chainsafe/netmask": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@chainsafe/netmask/-/netmask-2.0.0.tgz", "integrity": "sha512-I3Z+6SWUoaljh3TBzCnCxjlUyN8tA+NAk5L6m9IxvCf1BENQTePzPMis97CoN/iMW1St3WN+AWCCRp+TTBRiDg==", "dependencies": {"@chainsafe/is-ip": "^2.0.1"}}, "node_modules/@helia/bitswap": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@helia/bitswap/-/bitswap-2.1.2.tgz", "integrity": "sha512-gleOYd5jJPA0jVkm/LKkfEh+Z/TQNviv4e8pCdPlD3V/Sg6q0htdO/ttlAlMyYGY6187fwsCP+fZkqWNao/h9Q==", "dependencies": {"@helia/interface": "^5.3.2", "@helia/utils": "^1.3.2", "@libp2p/interface": "^2.2.1", "@libp2p/logger": "^5.1.4", "@libp2p/peer-collections": "^6.0.12", "@libp2p/utils": "^6.2.1", "@multiformats/multiaddr": "^12.3.3", "any-signal": "^4.1.1", "interface-blockstore": "^5.3.1", "interface-store": "^6.0.2", "it-drain": "^3.0.7", "it-length-prefixed": "^10.0.1", "it-map": "^3.1.1", "it-pipe": "^3.0.1", "it-take": "^3.0.6", "multiformats": "^13.3.1", "p-defer": "^4.0.1", "progress-events": "^1.0.1", "protons-runtime": "^5.5.0", "race-event": "^1.3.0", "uint8-varint": "^2.0.4", "uint8arraylist": "^2.4.8", "uint8arrays": "^5.1.0"}}, "node_modules/@helia/bitswap/node_modules/any-signal": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/any-signal/-/any-signal-4.1.1.tgz", "integrity": "sha512-iADenERppdC+A2YKbOXXB2WUeABLaM6qnpZ70kZbPZ1cZMMJ7eF+3CaYm+/PhBizgkzlvssC7QuHS30oOiQYWA==", "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@helia/bitswap/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/@helia/bitswap/node_modules/it-map": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/it-map/-/it-map-3.1.3.tgz", "integrity": "sha512-BAdTuPN/Ie5K4pKLShqyLGBvkLSPtraYXBrX8h+Ki1CZQI8o0dOcaLewISLTXmEJsOHcAjkwxJsVwxND4/Rkpg==", "dependencies": {"it-peekable": "^3.0.0"}}, "node_modules/@helia/bitswap/node_modules/it-peekable": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/it-peekable/-/it-peekable-3.0.7.tgz", "integrity": "sha512-w9W0WzNCsHLctV0z6vAA6N3jPgJu0qZZVlhngS+L29Rdva940f4Ea4ubtEXXYVBbq3l9Woo1MdWLGiEXzQDtdg=="}, "node_modules/@helia/bitswap/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@helia/bitswap/node_modules/p-defer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-4.0.1.tgz", "integrity": "sha512-Mr5KC5efvAK5VUptYEIopP1bakB85k2IWXaRC0rsh1uwn1L6M0LVml8OIQ4Gudg4oyZakf7FmeRLkMMtZW1i5A==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@helia/bitswap/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@helia/block-brokers": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/@helia/block-brokers/-/block-brokers-4.2.2.tgz", "integrity": "sha512-2Iq5basphEn8RcRK+wMiSfy5EJWgjI69wKH8CB66dSyaiduV7etP8E69eP5oGv43ShPycel4Xnmb2mNl6WWp5w==", "dependencies": {"@helia/bitswap": "^2.1.2", "@helia/interface": "^5.3.2", "@helia/utils": "^1.3.2", "@libp2p/interface": "^2.2.1", "@libp2p/utils": "^6.2.1", "@multiformats/multiaddr": "^12.3.3", "@multiformats/multiaddr-matcher": "^1.6.0", "@multiformats/multiaddr-to-uri": "^11.0.0", "interface-blockstore": "^5.3.1", "interface-store": "^6.0.2", "multiformats": "^13.3.1", "progress-events": "^1.0.1"}}, "node_modules/@helia/block-brokers/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/@helia/block-brokers/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@helia/delegated-routing-v1-http-api-client": {"version": "4.2.5", "resolved": "https://registry.npmjs.org/@helia/delegated-routing-v1-http-api-client/-/delegated-routing-v1-http-api-client-4.2.5.tgz", "integrity": "sha512-fFqVhs7a4TnpKQ1cZ4im3tj53v+8UZLFkQo85otl/GpbIVBmBoGbjkDHGPv4UdjJ2lmYM/cRdnHsYbfjuc5pwA==", "dependencies": {"@libp2p/interface": "^2.2.0", "@libp2p/logger": "^5.0.1", "@libp2p/peer-id": "^5.0.1", "@multiformats/multiaddr": "^12.3.1", "any-signal": "^4.1.1", "browser-readablestream-to-it": "^2.0.7", "ipns": "^10.0.0", "it-first": "^3.0.6", "it-map": "^3.1.1", "it-ndjson": "^1.0.7", "multiformats": "^13.3.0", "p-defer": "^4.0.1", "p-queue": "^8.0.1", "uint8arrays": "^5.1.0"}}, "node_modules/@helia/delegated-routing-v1-http-api-client/node_modules/any-signal": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/any-signal/-/any-signal-4.1.1.tgz", "integrity": "sha512-iADenERppdC+A2YKbOXXB2WUeABLaM6qnpZ70kZbPZ1cZMMJ7eF+3CaYm+/PhBizgkzlvssC7QuHS30oOiQYWA==", "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@helia/delegated-routing-v1-http-api-client/node_modules/browser-readablestream-to-it": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/browser-readablestream-to-it/-/browser-readablestream-to-it-2.0.9.tgz", "integrity": "sha512-f/yOqsXAC1tTJuq4vK1JSGLDTlK08XxTAAzoM5ePJhddySkI1yh/VjNoo0LACxwy+M1PV1xvD1OBJdToZ877ew=="}, "node_modules/@helia/delegated-routing-v1-http-api-client/node_modules/it-first": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-first/-/it-first-3.0.8.tgz", "integrity": "sha512-neaRRwOMCmMKkXJVZ4bvUDVlde+Xh0aTWr7hFaOZeDXzbctGVV/WHmPVqBqy3RjlsP7eRM0vcqNtlM8hivcmGw=="}, "node_modules/@helia/delegated-routing-v1-http-api-client/node_modules/it-map": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/it-map/-/it-map-3.1.3.tgz", "integrity": "sha512-BAdTuPN/Ie5K4pKLShqyLGBvkLSPtraYXBrX8h+Ki1CZQI8o0dOcaLewISLTXmEJsOHcAjkwxJsVwxND4/Rkpg==", "dependencies": {"it-peekable": "^3.0.0"}}, "node_modules/@helia/delegated-routing-v1-http-api-client/node_modules/it-peekable": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/it-peekable/-/it-peekable-3.0.7.tgz", "integrity": "sha512-w9W0WzNCsHLctV0z6vAA6N3jPgJu0qZZVlhngS+L29Rdva940f4Ea4ubtEXXYVBbq3l9Woo1MdWLGiEXzQDtdg=="}, "node_modules/@helia/delegated-routing-v1-http-api-client/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@helia/delegated-routing-v1-http-api-client/node_modules/p-defer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-4.0.1.tgz", "integrity": "sha512-Mr5KC5efvAK5VUptYEIopP1bakB85k2IWXaRC0rsh1uwn1L6M0LVml8OIQ4Gudg4oyZakf7FmeRLkMMtZW1i5A==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@helia/delegated-routing-v1-http-api-client/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@helia/http": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/@helia/http/-/http-2.1.2.tgz", "integrity": "sha512-jyVlLxZlPmH4fcdvqrYM7BUpsDGEenuNY6rrlGPny5NvdbbgLxvs8fnNpUe5swOQ8jOM+WCzsv4wQEHrU9AqdQ==", "dependencies": {"@helia/block-brokers": "^4.2.2", "@helia/interface": "^5.3.2", "@helia/routers": "^3.1.2", "@helia/utils": "^1.3.2", "blockstore-core": "^5.0.2", "datastore-core": "^10.0.2"}}, "node_modules/@helia/interface": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/@helia/interface/-/interface-5.3.2.tgz", "integrity": "sha512-+NOQrBxsz6i0FOsz16OgTlGAcyQ+cSYGzd9VJE7cMMrrGo9IcDEKp5Kvh980Ajg1LJFgeqYyqmxefnPExUUsBg==", "dependencies": {"@libp2p/interface": "^2.2.1", "@multiformats/dns": "^1.0.6", "@multiformats/multiaddr": "^12.4.0", "interface-blockstore": "^5.3.1", "interface-datastore": "^8.3.1", "interface-store": "^6.0.2", "multiformats": "^13.3.1", "progress-events": "^1.0.1"}}, "node_modules/@helia/interface/node_modules/interface-datastore": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/interface-datastore/-/interface-datastore-8.3.2.tgz", "integrity": "sha512-R3NLts7pRbJKc3qFdQf+u40hK8XWc0w4Qkx3OFEstC80VoaDUABY/dXA2EJPhtNC+bsrf41Ehvqb6+pnIclyRA==", "dependencies": {"interface-store": "^6.0.0", "uint8arrays": "^5.1.0"}}, "node_modules/@helia/interface/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/@helia/interface/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@helia/interface/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@helia/routers": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@helia/routers/-/routers-3.1.2.tgz", "integrity": "sha512-Fq5mj7JkeDVHXUak+HJYIK7TGjKGErvWzyjjcOMNrCk0UyWCfLcw3iFwWWfkMOP+e8IN7bgPxXVxxyOir9iL+w==", "dependencies": {"@helia/delegated-routing-v1-http-api-client": "^4.2.1", "@helia/interface": "^5.3.2", "@libp2p/interface": "^2.2.1", "@libp2p/peer-id": "^5.0.8", "@multiformats/uri-to-multiaddr": "^9.0.1", "ipns": "^10.0.0", "it-first": "^3.0.6", "it-map": "^3.1.1", "multiformats": "^13.3.1", "uint8arrays": "^5.1.0"}}, "node_modules/@helia/routers/node_modules/it-first": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-first/-/it-first-3.0.8.tgz", "integrity": "sha512-neaRRwOMCmMKkXJVZ4bvUDVlde+Xh0aTWr7hFaOZeDXzbctGVV/WHmPVqBqy3RjlsP7eRM0vcqNtlM8hivcmGw=="}, "node_modules/@helia/routers/node_modules/it-map": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/it-map/-/it-map-3.1.3.tgz", "integrity": "sha512-BAdTuPN/Ie5K4pKLShqyLGBvkLSPtraYXBrX8h+Ki1CZQI8o0dOcaLewISLTXmEJsOHcAjkwxJsVwxND4/Rkpg==", "dependencies": {"it-peekable": "^3.0.0"}}, "node_modules/@helia/routers/node_modules/it-peekable": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/it-peekable/-/it-peekable-3.0.7.tgz", "integrity": "sha512-w9W0WzNCsHLctV0z6vAA6N3jPgJu0qZZVlhngS+L29Rdva940f4Ea4ubtEXXYVBbq3l9Woo1MdWLGiEXzQDtdg=="}, "node_modules/@helia/routers/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@helia/routers/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@helia/unixfs": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/@helia/unixfs/-/unixfs-5.0.3.tgz", "integrity": "sha512-Z9R1l2Dso4CtgGqavwko2nEnNSSoOB8uWnkmmOE7C47uzQ2W/d5YIDmgPkptrSkT7h/OPH2O1ctKgLqsSLIAPQ==", "dependencies": {"@helia/interface": "^5.3.2", "@ipld/dag-pb": "^4.1.3", "@libp2p/interface": "^2.2.1", "@libp2p/logger": "^5.1.4", "@libp2p/utils": "^6.6.0", "@multiformats/murmur3": "^2.1.8", "hamt-sharding": "^3.0.6", "interface-blockstore": "^5.3.1", "ipfs-unixfs": "^11.2.0", "ipfs-unixfs-exporter": "^13.6.1", "ipfs-unixfs-importer": "^15.3.1", "it-all": "^3.0.6", "it-first": "^3.0.6", "it-glob": "^3.0.1", "it-last": "^3.0.6", "it-pipe": "^3.0.1", "multiformats": "^13.3.1", "progress-events": "^1.0.1", "sparse-array": "^1.3.2", "uint8arrays": "^5.1.0"}}, "node_modules/@helia/unixfs/node_modules/@ipld/dag-pb": {"version": "4.1.5", "resolved": "https://registry.npmjs.org/@ipld/dag-pb/-/dag-pb-4.1.5.tgz", "integrity": "sha512-w4PZ2yPqvNmlAir7/2hsCRMqny1EY5jj26iZcSgxREJexmbAc2FI21jp26MqiNdfgAxvkCnf2N/TJI18GaDNwA==", "dependencies": {"multiformats": "^13.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@helia/unixfs/node_modules/ipfs-unixfs": {"version": "11.2.1", "resolved": "https://registry.npmjs.org/ipfs-unixfs/-/ipfs-unixfs-11.2.1.tgz", "integrity": "sha512-gUeeX63EFgiaMgcs0cUs2ZUPvlOeEZ38okjK8twdWGZX2jYd2rCk8k/TJ3DSRIDZ2t/aZMv6I23guxHaofZE3w==", "dependencies": {"protons-runtime": "^5.5.0", "uint8arraylist": "^2.4.8"}}, "node_modules/@helia/unixfs/node_modules/it-all": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-all/-/it-all-3.0.8.tgz", "integrity": "sha512-TFAXqUjwuPFhyktbU7XIOjdvqjpc/c2xvDYfCrfHA6HP68+EQDCXuwGJ9YchvZTyXSaB2fkX3lI9aybcFUHWUw=="}, "node_modules/@helia/unixfs/node_modules/it-first": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-first/-/it-first-3.0.8.tgz", "integrity": "sha512-neaRRwOMCmMKkXJVZ4bvUDVlde+Xh0aTWr7hFaOZeDXzbctGVV/WHmPVqBqy3RjlsP7eRM0vcqNtlM8hivcmGw=="}, "node_modules/@helia/unixfs/node_modules/it-glob": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/it-glob/-/it-glob-3.0.3.tgz", "integrity": "sha512-sLf7O4otUWG370biMC0Hz+HQGkPg6YH7v1eIK7WvIHavMSYsKaSuH82ZNn0aCFAgFirM4zLL29ig9wuWg30iNg==", "dependencies": {"fast-glob": "^3.3.3"}}, "node_modules/@helia/unixfs/node_modules/it-last": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-last/-/it-last-3.0.8.tgz", "integrity": "sha512-sdzoMeMAIJmRucZTnRd1GTtcoGV2EAS81fXfRKCVLviEX1wcvHhE43G0b/aKFFPc6ypuHWZR8vxaoHtDz/6b/A=="}, "node_modules/@helia/unixfs/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@helia/unixfs/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@helia/utils": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/@helia/utils/-/utils-1.3.2.tgz", "integrity": "sha512-epMlRqLyJ95NDem4jc6kNRUlWkW5+HOwxvgJzOn70mMdXFkyYIOx24BpZurCFi+Cg6/k9vSfKsU5V1teWxYfRQ==", "dependencies": {"@helia/interface": "^5.3.2", "@ipld/dag-cbor": "^9.2.2", "@ipld/dag-json": "^10.2.3", "@ipld/dag-pb": "^4.1.3", "@libp2p/interface": "^2.5.0", "@libp2p/logger": "^5.1.8", "@libp2p/utils": "^6.5.1", "@multiformats/dns": "^1.0.6", "@multiformats/multiaddr": "^12.4.0", "any-signal": "^4.1.1", "blockstore-core": "^5.0.2", "cborg": "^4.2.6", "interface-blockstore": "^5.3.1", "interface-datastore": "^8.3.1", "interface-store": "^6.0.2", "it-drain": "^3.0.7", "it-filter": "^3.1.1", "it-foreach": "^2.1.1", "it-merge": "^3.0.5", "mortice": "^3.0.6", "multiformats": "^13.3.1", "p-defer": "^4.0.1", "progress-events": "^1.0.1", "uint8arrays": "^5.1.0"}}, "node_modules/@helia/utils/node_modules/@ipld/dag-cbor": {"version": "9.2.4", "resolved": "https://registry.npmjs.org/@ipld/dag-cbor/-/dag-cbor-9.2.4.tgz", "integrity": "sha512-GbDWYl2fdJgkYtIJN0HY9oO0o50d1nB4EQb7uYWKUd2ztxCjxiEW3PjwGG0nqUpN1G4Cug6LX8NzbA7fKT+zfA==", "dependencies": {"cborg": "^4.0.0", "multiformats": "^13.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@helia/utils/node_modules/@ipld/dag-json": {"version": "10.2.5", "resolved": "https://registry.npmjs.org/@ipld/dag-json/-/dag-json-10.2.5.tgz", "integrity": "sha512-Q4Fr3IBDEN8gkpgNefynJ4U/ZO5Kwr7WSUMBDbZx0c37t0+IwQCTM9yJh8l5L4SRFjm31MuHwniZ/kM+P7GQ3Q==", "dependencies": {"cborg": "^4.0.0", "multiformats": "^13.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@helia/utils/node_modules/@ipld/dag-pb": {"version": "4.1.5", "resolved": "https://registry.npmjs.org/@ipld/dag-pb/-/dag-pb-4.1.5.tgz", "integrity": "sha512-w4PZ2yPqvNmlAir7/2hsCRMqny1EY5jj26iZcSgxREJexmbAc2FI21jp26MqiNdfgAxvkCnf2N/TJI18GaDNwA==", "dependencies": {"multiformats": "^13.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@helia/utils/node_modules/any-signal": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/any-signal/-/any-signal-4.1.1.tgz", "integrity": "sha512-iADenERppdC+A2YKbOXXB2WUeABLaM6qnpZ70kZbPZ1cZMMJ7eF+3CaYm+/PhBizgkzlvssC7QuHS30oOiQYWA==", "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@helia/utils/node_modules/cborg": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/cborg/-/cborg-4.2.11.tgz", "integrity": "sha512-7gs3iaqtsD9OHowgqzc6ixQGwSBONqosVR2co0Bg0pARgrLap+LCcEIXJuuIz2jHy0WWQeDMFPEsU2r17I2XPQ==", "bin": {"cborg": "lib/bin.js"}}, "node_modules/@helia/utils/node_modules/interface-datastore": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/interface-datastore/-/interface-datastore-8.3.2.tgz", "integrity": "sha512-R3NLts7pRbJKc3qFdQf+u40hK8XWc0w4Qkx3OFEstC80VoaDUABY/dXA2EJPhtNC+bsrf41Ehvqb6+pnIclyRA==", "dependencies": {"interface-store": "^6.0.0", "uint8arrays": "^5.1.0"}}, "node_modules/@helia/utils/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/@helia/utils/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@helia/utils/node_modules/p-defer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-4.0.1.tgz", "integrity": "sha512-Mr5KC5efvAK5VUptYEIopP1bakB85k2IWXaRC0rsh1uwn1L6M0LVml8OIQ4Gudg4oyZakf7FmeRLkMMtZW1i5A==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@helia/utils/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@ipld/dag-cbor": {"version": "7.0.3", "resolved": "https://registry.npmjs.org/@ipld/dag-cbor/-/dag-cbor-7.0.3.tgz", "integrity": "sha512-1VVh2huHsuohdXC1bGJNE8WR72slZ9XE2T3wbBBq31dm7ZBatmKLLxrB+XAqafxfRFjv08RZmj/W/ZqaM13AuA==", "dependencies": {"cborg": "^1.6.0", "multiformats": "^9.5.4"}}, "node_modules/@ipld/dag-json": {"version": "8.0.11", "resolved": "https://registry.npmjs.org/@ipld/dag-json/-/dag-json-8.0.11.tgz", "integrity": "sha512-Pea7JXeYHTWXRTIhBqBlhw7G53PJ7yta3G/sizGEZyzdeEwhZRr0od5IQ0r2ZxOt1Do+2czddjeEPp+YTxDwCA==", "dependencies": {"cborg": "^1.5.4", "multiformats": "^9.5.4"}}, "node_modules/@ipld/dag-pb": {"version": "2.1.18", "resolved": "https://registry.npmjs.org/@ipld/dag-pb/-/dag-pb-2.1.18.tgz", "integrity": "sha512-ZBnf2fuX9y3KccADURG5vb9FaOeMjFkCrNysB0PtftME/4iCTjxfaLoNq/IAh5fTqUOMXvryN6Jyka4ZGuMLIg==", "dependencies": {"multiformats": "^9.5.4"}}, "node_modules/@leichtgewicht/ip-codec": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz", "integrity": "sha512-Vo+PSpZG2/fmgmiNzYK9qWRh8h/CHrwD0mo1h1DzL4yzHNSfWYujGTYsWGreD000gcgmZ7K4Ys6Tx9TxtsKdDw=="}, "node_modules/@libp2p/crypto": {"version": "5.1.4", "resolved": "https://registry.npmjs.org/@libp2p/crypto/-/crypto-5.1.4.tgz", "integrity": "sha512-w9UNkjX7xhExr9PJeFeu6xy6q8ZR0FhoauI1qnN+dZsAlSQhRMzPvMdMJoSoGu/Ha9BbJ3Z2kpSS28GXHnL0rg==", "dependencies": {"@libp2p/interface": "^2.10.2", "@noble/curves": "^1.9.1", "@noble/hashes": "^1.8.0", "multiformats": "^13.3.4", "protons-runtime": "^5.5.0", "uint8arraylist": "^2.4.8", "uint8arrays": "^5.1.0"}}, "node_modules/@libp2p/crypto/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@libp2p/crypto/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@libp2p/interface": {"version": "2.10.2", "resolved": "https://registry.npmjs.org/@libp2p/interface/-/interface-2.10.2.tgz", "integrity": "sha512-aQ9jZaZZq9/y9NFax0K8Z6tTqLg3Qsyv1EY0O+eFrltmIkoUOCeCAybUTiOTwieknznHc9AMrHifaK3BYb+bqg==", "dependencies": {"@multiformats/multiaddr": "^12.4.0", "it-pushable": "^3.2.3", "it-stream-types": "^2.0.2", "multiformats": "^13.3.4", "progress-events": "^1.0.1", "uint8arraylist": "^2.4.8"}}, "node_modules/@libp2p/interface/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@libp2p/logger": {"version": "5.1.18", "resolved": "https://registry.npmjs.org/@libp2p/logger/-/logger-5.1.18.tgz", "integrity": "sha512-venbhUr6riuMTu8c8bnphZ27XcOuDDq6fdYaRVggokQpwSC6QRejlaFnY8w0K1ZuwFv9uc19UA2ELtOBQ26AJw==", "dependencies": {"@libp2p/interface": "^2.10.2", "@multiformats/multiaddr": "^12.4.0", "interface-datastore": "^8.3.1", "multiformats": "^13.3.4", "weald": "^1.0.4"}}, "node_modules/@libp2p/logger/node_modules/interface-datastore": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/interface-datastore/-/interface-datastore-8.3.2.tgz", "integrity": "sha512-R3NLts7pRbJKc3qFdQf+u40hK8XWc0w4Qkx3OFEstC80VoaDUABY/dXA2EJPhtNC+bsrf41Ehvqb6+pnIclyRA==", "dependencies": {"interface-store": "^6.0.0", "uint8arrays": "^5.1.0"}}, "node_modules/@libp2p/logger/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/@libp2p/logger/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@libp2p/logger/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@libp2p/peer-collections": {"version": "6.0.30", "resolved": "https://registry.npmjs.org/@libp2p/peer-collections/-/peer-collections-6.0.30.tgz", "integrity": "sha512-oN+ns04ka8X4gUdgXcxNwRsSuno2M9sWjrvOQ/L+TM96/H2dRK9wq/6Kva9rUQdszaP5rT8ooT2HjbyUt/omSw==", "dependencies": {"@libp2p/interface": "^2.10.2", "@libp2p/peer-id": "^5.1.5", "@libp2p/utils": "^6.6.5", "multiformats": "^13.3.4"}}, "node_modules/@libp2p/peer-collections/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@libp2p/peer-id": {"version": "5.1.5", "resolved": "https://registry.npmjs.org/@libp2p/peer-id/-/peer-id-5.1.5.tgz", "integrity": "sha512-WKlZLKkmzfAANR8mytvYz8hY8YOukMG5t4svsXvLMaVkWPFg9Mm+USTwVCCWRXOXAGQSxLuwpvzwOBJAZMb5IA==", "dependencies": {"@libp2p/crypto": "^5.1.4", "@libp2p/interface": "^2.10.2", "multiformats": "^13.3.4", "uint8arrays": "^5.1.0"}}, "node_modules/@libp2p/peer-id/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@libp2p/peer-id/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@libp2p/utils": {"version": "6.6.5", "resolved": "https://registry.npmjs.org/@libp2p/utils/-/utils-6.6.5.tgz", "integrity": "sha512-QU0dpwNv2bDqUsSXe/5kpKi7gl0CDZC9eSQoueQb9eH4ePVh7SvQjt8gCq6zF6aDHfJ+h/trAKSRk8BiPWKSRA==", "dependencies": {"@chainsafe/is-ip": "^2.1.0", "@chainsafe/netmask": "^2.0.0", "@libp2p/crypto": "^5.1.4", "@libp2p/interface": "^2.10.2", "@libp2p/logger": "^5.1.18", "@multiformats/multiaddr": "^12.4.0", "@sindresorhus/fnv1a": "^3.1.0", "any-signal": "^4.1.1", "delay": "^6.0.0", "get-iterator": "^2.0.1", "is-loopback-addr": "^2.0.2", "is-plain-obj": "^4.1.0", "it-foreach": "^2.1.3", "it-pipe": "^3.0.1", "it-pushable": "^3.2.3", "it-stream-types": "^2.0.2", "netmask": "^2.0.2", "p-defer": "^4.0.1", "race-event": "^1.3.0", "race-signal": "^1.1.3", "uint8arraylist": "^2.4.8", "uint8arrays": "^5.1.0"}}, "node_modules/@libp2p/utils/node_modules/any-signal": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/any-signal/-/any-signal-4.1.1.tgz", "integrity": "sha512-iADenERppdC+A2YKbOXXB2WUeABLaM6qnpZ70kZbPZ1cZMMJ7eF+3CaYm+/PhBizgkzlvssC7QuHS30oOiQYWA==", "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@libp2p/utils/node_modules/get-iterator": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/get-iterator/-/get-iterator-2.0.1.tgz", "integrity": "sha512-7HuY/hebu4gryTDT7O/XY/fvY9wRByEGdK6QOa4of8npTcv0+NS6frFKABcf6S9EBAsveTuKTsZQQBFMMNILIg=="}, "node_modules/@libp2p/utils/node_modules/is-plain-obj": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz", "integrity": "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@libp2p/utils/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@libp2p/utils/node_modules/p-defer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-4.0.1.tgz", "integrity": "sha512-Mr5KC5efvAK5VUptYEIopP1bakB85k2IWXaRC0rsh1uwn1L6M0LVml8OIQ4Gudg4oyZakf7FmeRLkMMtZW1i5A==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@libp2p/utils/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@multiformats/dns": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/@multiformats/dns/-/dns-1.0.6.tgz", "integrity": "sha512-nt/5UqjMPtyvkG9BQYdJ4GfLK3nMqGpFZOzf4hAmIa0sJh2LlS9YKXZ4FgwBDsaHvzZqR/rUFIywIc7pkHNNuw==", "dependencies": {"@types/dns-packet": "^5.6.5", "buffer": "^6.0.3", "dns-packet": "^5.6.1", "hashlru": "^2.3.0", "p-queue": "^8.0.1", "progress-events": "^1.0.0", "uint8arrays": "^5.0.2"}}, "node_modules/@multiformats/dns/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@multiformats/dns/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@multiformats/multiaddr": {"version": "12.4.0", "resolved": "https://registry.npmjs.org/@multiformats/multiaddr/-/multiaddr-12.4.0.tgz", "integrity": "sha512-FL7yBTLijJ5JkO044BGb2msf+uJLrwpD6jD6TkXlbjA9N12+18HT40jvd4o5vL4LOJMc86dPX6tGtk/uI9kYKg==", "dependencies": {"@chainsafe/is-ip": "^2.0.1", "@chainsafe/netmask": "^2.0.0", "@multiformats/dns": "^1.0.3", "multiformats": "^13.0.0", "uint8-varint": "^2.0.1", "uint8arrays": "^5.0.0"}}, "node_modules/@multiformats/multiaddr-matcher": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/@multiformats/multiaddr-matcher/-/multiaddr-matcher-1.7.2.tgz", "integrity": "sha512-BJzHOBAAxGZKw+FY/MzeIKGKERAW/1XOrpj61wgzZVvR/iksyGTQhliyTgmuakpBJPSsCxlrk3eLemVhZuJIFQ==", "dependencies": {"@chainsafe/is-ip": "^2.0.1", "@multiformats/multiaddr": "^12.0.0", "multiformats": "^13.0.0"}}, "node_modules/@multiformats/multiaddr-matcher/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@multiformats/multiaddr-to-uri": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/@multiformats/multiaddr-to-uri/-/multiaddr-to-uri-11.0.0.tgz", "integrity": "sha512-9RNmlIGwZbBLsHekT50dbt4o4u8Iciw9kGjv+WHiGxQdsJ6xKKjU1+C0Vbas6RilMbaVOAOnEyfNcXbUmTkLxQ==", "dependencies": {"@multiformats/multiaddr": "^12.3.0"}}, "node_modules/@multiformats/multiaddr/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@multiformats/multiaddr/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/@multiformats/murmur3": {"version": "2.1.8", "resolved": "https://registry.npmjs.org/@multiformats/murmur3/-/murmur3-2.1.8.tgz", "integrity": "sha512-6vId1C46ra3R1sbJUOFCZnsUIveR9oF20yhPmAFxPm0JfrX3/ZRCgP3YDrBzlGoEppOXnA9czHeYc0T9mB6hbA==", "dependencies": {"multiformats": "^13.0.0", "murmurhash3js-revisited": "^3.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/@multiformats/murmur3/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/@multiformats/uri-to-multiaddr": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/@multiformats/uri-to-multiaddr/-/uri-to-multiaddr-9.0.1.tgz", "integrity": "sha512-xH2nIA98EGNfrjt4A3wiT9xw8bg7HkrRhSIUTb8am9flxweu6ObUZSAhSmaRMBWL1UnlBMPDiGNYGxVlhmygBQ==", "dependencies": {"@multiformats/multiaddr": "^12.1.14", "is-ip": "^5.0.0"}}, "node_modules/@multiformats/uri-to-multiaddr/node_modules/ip-regex": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/ip-regex/-/ip-regex-5.0.0.tgz", "integrity": "sha512-fOCG6lhoKKakwv+C6KdsOnGvgXnmgfmp0myi3bcNwj3qfwPAxRKWEuFhvEFF7ceYIz6+1jRZ+yguLFAmUNPEfw==", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@multiformats/uri-to-multiaddr/node_modules/is-ip": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/is-ip/-/is-ip-5.0.1.tgz", "integrity": "sha512-FCsGHdlrOnZQcp0+XT5a+pYowf33itBalCl+7ovNXC/7o5BhIpG14M3OrpPPdBSIQJCm+0M5+9mO7S9VVTTCFw==", "dependencies": {"ip-regex": "^5.0.0", "super-regex": "^0.2.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@noble/curves": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/@noble/curves/-/curves-1.9.1.tgz", "integrity": "sha512-k11yZxZg+t+gWvBbIswW0yoJlu8cHOC7dhunwOzoWH/mXGBiYyR4YY6hAEK/3EUs4UpB8la1RfdRpeGsFHkWsA==", "dependencies": {"@noble/hashes": "1.8.0"}, "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/hashes": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz", "integrity": "sha512-jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A==", "engines": {"node": "^14.21.3 || >=16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "integrity": "sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ=="}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz", "integrity": "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg=="}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz", "integrity": "sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg=="}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "integrity": "sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q=="}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz", "integrity": "sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==", "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz", "integrity": "sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ=="}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz", "integrity": "sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q=="}, "node_modules/@protobufjs/path": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz", "integrity": "sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA=="}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz", "integrity": "sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw=="}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz", "integrity": "sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw=="}, "node_modules/@sindresorhus/fnv1a": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@sindresorhus/fnv1a/-/fnv1a-3.1.0.tgz", "integrity": "sha512-KV321z5m/0nuAg83W1dPLy85HpHDk7Sdi4fJbwvacWsEhAh+rZUW4ZfGcXmUIvjZg4ss2bcwNlRhJ7GBEUG08w==", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@types/dns-packet": {"version": "5.6.5", "resolved": "https://registry.npmjs.org/@types/dns-packet/-/dns-packet-5.6.5.tgz", "integrity": "sha512-qXOC7XLOEe43ehtWJCMnQXvgcIpv6rPmQ1jXT98Ad8A3TB1Ue50jsCbSSSyuazScEuZ/Q026vHbrOTVkmwA+7Q==", "dependencies": {"@types/node": "*"}}, "node_modules/@types/long": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/@types/long/-/long-4.0.2.tgz", "integrity": "sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA=="}, "node_modules/@types/minimatch": {"version": "3.0.5", "resolved": "https://registry.npmjs.org/@types/minimatch/-/minimatch-3.0.5.tgz", "integrity": "sha512-Klz949h02Gz2uZCMGwDUSDS1YBlTdDDgbWHi+81l29tQALUtvz4rAYi5uoVhE5Lagoq6DeqAUlbrHvW/mXDgdQ=="}, "node_modules/@types/node": {"version": "22.15.24", "resolved": "https://registry.npmjs.org/@types/node/-/node-22.15.24.tgz", "integrity": "sha512-w9CZGm9RDjzTh/D+hFwlBJ3ziUaVw7oufKA3vOFSOZlzmW9AkZnfjPb+DLnrV6qtgL/LNmP0/2zBNCFHL3F0ng==", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/abort-error": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/abort-error/-/abort-error-1.0.1.tgz", "integrity": "sha512-fxqCblJiIPdSXIUrxI0PL+eJG49QdP9SQ70qtB65MVAoMr2rASlOyAbJFOylfB467F/f+5BCLJJq58RYi7mGfg=="}, "node_modules/any-signal": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/any-signal/-/any-signal-3.0.1.tgz", "integrity": "sha512-xgZgJtKEa9YmDqXodIgl7Fl1C8yNXr8w6gXjqK3LW4GcEiYT+6AQfJSE/8SPsEpLLmcvbv8YU+qet94UewHxqg=="}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "node_modules/axios": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz", "integrity": "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/bl": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/bl/-/bl-5.1.0.tgz", "integrity": "sha512-tv1ZJHLfTDnXE6tMHv73YgSJaWR2AFuPwMntBe7XL/GBFHnT0CLnsHMogfk5+GzCDC5ZWarSCYaIGATZt9dNsQ==", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/blob-to-it": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/blob-to-it/-/blob-to-it-1.0.4.tgz", "integrity": "sha512-iCmk0W4NdbrWgRRuxOriU8aM5ijeVLI61Zulsmg/lUHNr7pYjoj+U77opLefNagevtrrbMt3JQ5Qip7ar178kA==", "dependencies": {"browser-readablestream-to-it": "^1.0.3"}}, "node_modules/blockstore-core": {"version": "5.0.4", "resolved": "https://registry.npmjs.org/blockstore-core/-/blockstore-core-5.0.4.tgz", "integrity": "sha512-v7wtBEpW2J/kKljN7Z2u4Tnwr7qwnOvW1aPVfynIxEdejlVC7gg4z9k6iJt7n5XMGkdNnH4HOmVcjYcaMnu7yg==", "dependencies": {"@libp2p/logger": "^5.1.18", "interface-blockstore": "^5.0.0", "interface-store": "^6.0.0", "it-filter": "^3.1.3", "it-merge": "^3.0.11", "multiformats": "^13.3.6"}}, "node_modules/blockstore-core/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/blockstore-core/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browser-readablestream-to-it": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/browser-readablestream-to-it/-/browser-readablestream-to-it-1.0.3.tgz", "integrity": "sha512-+12sHB+Br8HIh6VAMVEG5r3UXCyESIgDW7kzk3BjIXa43DVqVwL7GC5TW3jeh+72dtcH99pPVpw0X8i0jt+/kw=="}, "node_modules/buffer": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/cborg": {"version": "1.10.2", "resolved": "https://registry.npmjs.org/cborg/-/cborg-1.10.2.tgz", "integrity": "sha512-b3tFPA9pUr2zCUiCfRd2+wok2/LBSNUMKOuRRok+WlvvAgEt/PlbgPTsZUcwCOs53IJvLgTp0eotwtosE6njug==", "bin": {"cborg": "cli.js"}}, "node_modules/clone-regexp": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/clone-regexp/-/clone-regexp-3.0.0.tgz", "integrity": "sha512-ujdnoq2Kxb8s3ItNBtnYeXdm07FcU0u8ARAT1lQ2YdMwQC+cdiXX8KoqMVuglztILivceTtp4ivqGSmEmhBUJw==", "dependencies": {"is-regexp": "^3.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="}, "node_modules/convert-hrtime": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/convert-hrtime/-/convert-hrtime-5.0.0.tgz", "integrity": "sha512-lOETlkIeYSJWcbbcvjRKGxVMXJR+8+OQb/mTPbA4ObPMytYIsUbuOE0Jzy60hjARYszq1id0j8KgVhC+WGZVTg==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/dag-jose": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/dag-jose/-/dag-jose-1.0.0.tgz", "integrity": "sha512-U0b/YsIPBp6YZNTFrVjwLZAlY3qGRxZTIEcM/CcQmrVrCWq9MWQq9pheXVSPLIhF4SNwzp2SikPva4/BIrJY+g==", "dependencies": {"@ipld/dag-cbor": "^6.0.3", "multiformats": "^9.0.2"}}, "node_modules/dag-jose/node_modules/@ipld/dag-cbor": {"version": "6.0.15", "resolved": "https://registry.npmjs.org/@ipld/dag-cbor/-/dag-cbor-6.0.15.tgz", "integrity": "sha512-Vm3VTSTwlmGV92a3C5aeY+r2A18zbH2amehNhsX8PBa3muXICaWrN8Uri85A5hLH7D7ElhE8PdjxD6kNqUmTZA==", "dependencies": {"cborg": "^1.5.4", "multiformats": "^9.5.4"}}, "node_modules/datastore-core": {"version": "10.0.4", "resolved": "https://registry.npmjs.org/datastore-core/-/datastore-core-10.0.4.tgz", "integrity": "sha512-IctgCO0GA7GHG7aRm3JRruibCsfvN4EXNnNIlLCZMKIv0TPkdAL5UFV3/xTYFYrrZ1jRNrXZNZRvfcVf/R+rAw==", "dependencies": {"@libp2p/logger": "^5.1.18", "interface-datastore": "^8.0.0", "interface-store": "^6.0.0", "it-drain": "^3.0.9", "it-filter": "^3.1.3", "it-map": "^3.1.3", "it-merge": "^3.0.11", "it-pipe": "^3.0.1", "it-sort": "^3.0.8", "it-take": "^3.0.8"}}, "node_modules/datastore-core/node_modules/interface-datastore": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/interface-datastore/-/interface-datastore-8.3.2.tgz", "integrity": "sha512-R3NLts7pRbJKc3qFdQf+u40hK8XWc0w4Qkx3OFEstC80VoaDUABY/dXA2EJPhtNC+bsrf41Ehvqb6+pnIclyRA==", "dependencies": {"interface-store": "^6.0.0", "uint8arrays": "^5.1.0"}}, "node_modules/datastore-core/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/datastore-core/node_modules/it-map": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/it-map/-/it-map-3.1.3.tgz", "integrity": "sha512-BAdTuPN/Ie5K4pKLShqyLGBvkLSPtraYXBrX8h+Ki1CZQI8o0dOcaLewISLTXmEJsOHcAjkwxJsVwxND4/Rkpg==", "dependencies": {"it-peekable": "^3.0.0"}}, "node_modules/datastore-core/node_modules/it-peekable": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/it-peekable/-/it-peekable-3.0.7.tgz", "integrity": "sha512-w9W0WzNCsHLctV0z6vAA6N3jPgJu0qZZVlhngS+L29Rdva940f4Ea4ubtEXXYVBbq3l9Woo1MdWLGiEXzQDtdg=="}, "node_modules/datastore-core/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/datastore-core/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/delay": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/delay/-/delay-6.0.0.tgz", "integrity": "sha512-2NJozoOHQ4NuZuVIr5CWd0iiLVIRSDepakaovIN+9eIDHEhdCAEvSy2cuf1DCrPPQLvHmbqTHODlhHg8UCy4zw==", "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "engines": {"node": ">=0.4.0"}}, "node_modules/dns-over-http-resolver": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/dns-over-http-resolver/-/dns-over-http-resolver-1.2.3.tgz", "integrity": "sha512-miDiVSI6KSNbi4SVifzO/reD8rMnxgrlnkrlkugOLQpWQTe2qMdHsZp5DmfKjxNE+/T3VAAYLQUZMv9SMr6+AA==", "dependencies": {"debug": "^4.3.1", "native-fetch": "^3.0.0", "receptacle": "^1.3.2"}}, "node_modules/dns-packet": {"version": "5.6.1", "resolved": "https://registry.npmjs.org/dns-packet/-/dns-packet-5.6.1.tgz", "integrity": "sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw==", "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "engines": {"node": ">=6"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/electron-fetch": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/electron-fetch/-/electron-fetch-1.9.1.tgz", "integrity": "sha512-M9qw6oUILGVrcENMSRRefE1MbHPIz0h79EKIeJWK9v563aT9Qkh8aEHPO1H5vi970wPirNY+jO9OpFoLiMsMGA==", "dependencies": {"encoding": "^0.1.13"}, "engines": {"node": ">=6"}}, "node_modules/encoding": {"version": "0.1.13", "resolved": "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/err-code": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/err-code/-/err-code-3.0.1.tgz", "integrity": "sha512-GiaH0KJUewYok+eeY05IIgjtAe4Yltygk9Wqp1V5yVWLdhf0hYZchRjNIT9bb0mSwRcIusT3cx7PJUf3zEIfUA=="}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/eventemitter3": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz", "integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA=="}, "node_modules/fast-fifo": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/fast-fifo/-/fast-fifo-1.3.2.tgz", "integrity": "sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ=="}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz", "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz", "integrity": "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function-timeout": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/function-timeout/-/function-timeout-0.1.1.tgz", "integrity": "sha512-0NVVC0TaP7dSTvn1yMiy6d6Q8gifzbvQafO46RtLG/kHJUBNd+pVRGOBoK44wNBvtSPUJRfdVvkFdD3p0xvyZg==", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-iterator": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/get-iterator/-/get-iterator-1.0.2.tgz", "integrity": "sha512-v+dm9bNVfOYsY1OrhaCrmyOcYoSeVvbt+hHZ0Au+T+p1y+0Uyj9aMaGIeUTT6xdpRbWzDeYKvfOslPhggQMcsg=="}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hamt-sharding": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/hamt-sharding/-/hamt-sharding-3.0.6.tgz", "integrity": "sha512-nZeamxfymIWLpVcAN0CRrb7uVq3hCOGj9IcL6NMA6VVCVWqj+h9Jo/SmaWuS92AEDf1thmHsM5D5c70hM3j2Tg==", "dependencies": {"sparse-array": "^1.3.1", "uint8arrays": "^5.0.1"}}, "node_modules/hamt-sharding/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/hamt-sharding/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hashlru": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/hashlru/-/hashlru-2.3.0.tgz", "integrity": "sha512-0cMsjjIC8I+D3M44pOQdsy0OHXGLVz6Z0beRuufhKa0KfaD2wGwAev6jILzXsd3/vpnNQJmWyZtIILqM1N+n5A=="}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "node_modules/interface-blockstore": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/interface-blockstore/-/interface-blockstore-5.3.2.tgz", "integrity": "sha512-oA9Pjkxun/JHAsZrYEyKX+EoPjLciTzidE7wipLc/3YoHDjzsnXRJzAzFJXNUvogtY4g7hIwxArx8+WKJs2RIg==", "dependencies": {"interface-store": "^6.0.0", "multiformats": "^13.3.6"}}, "node_modules/interface-blockstore/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/interface-blockstore/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/interface-datastore": {"version": "6.1.1", "resolved": "https://registry.npmjs.org/interface-datastore/-/interface-datastore-6.1.1.tgz", "integrity": "sha512-AmCS+9CT34pp2u0QQVXjKztkuq3y5T+BIciuiHDDtDZucZD8VudosnSdUyXJV6IsRkN5jc4RFDhCk1O6Q3Gxjg==", "dependencies": {"interface-store": "^2.0.2", "nanoid": "^3.0.2", "uint8arrays": "^3.0.0"}}, "node_modules/interface-store": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-2.0.2.tgz", "integrity": "sha512-rScRlhDcz6k199EkHqT8NpM87ebN89ICOzILoBHgaG36/WX50N32BnU/kpZgCGPLhARRAWUUX5/cyaIjt7Kipg=="}, "node_modules/ip-regex": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ip-regex/-/ip-regex-4.3.0.tgz", "integrity": "sha512-B9ZWJxHHOHUhUjCPrMpLD4xEq35bUTClHM1S6CBU5ixQnkZmwipwgc96vAd7AAGM9TGHvJR+Uss+/Ak6UphK+Q==", "engines": {"node": ">=8"}}, "node_modules/ipfs-core-types": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/ipfs-core-types/-/ipfs-core-types-0.10.3.tgz", "integrity": "sha512-GNid2lRBjR5qgScCglgk7w9Hk3TZAwPHQXxOLQx72wgyc0jF2U5NXRoKW0GRvX8NPbHmsrFszForIqxd23I1Gw==", "deprecated": "js-IPFS has been deprecated in favour of Helia - please see https://github.com/ipfs/js-ipfs/issues/4336 for details", "dependencies": {"@ipld/dag-pb": "^2.1.3", "interface-datastore": "^6.0.2", "ipfs-unixfs": "^6.0.3", "multiaddr": "^10.0.0", "multiformats": "^9.5.1"}}, "node_modules/ipfs-core-utils": {"version": "0.14.3", "resolved": "https://registry.npmjs.org/ipfs-core-utils/-/ipfs-core-utils-0.14.3.tgz", "integrity": "sha512-aBkewVhgAj3NWXPwu6imj0wADGiGVZmJzqKzODOJsibDjkx6FGdMv8kvvqtLnK8LS/dvSk9yk32IDtuDyYoV7Q==", "deprecated": "js-IPFS has been deprecated in favour of Helia - please see https://github.com/ipfs/js-ipfs/issues/4336 for details", "dependencies": {"any-signal": "^3.0.0", "blob-to-it": "^1.0.1", "browser-readablestream-to-it": "^1.0.1", "debug": "^4.1.1", "err-code": "^3.0.1", "ipfs-core-types": "^0.10.3", "ipfs-unixfs": "^6.0.3", "ipfs-utils": "^9.0.6", "it-all": "^1.0.4", "it-map": "^1.0.4", "it-peekable": "^1.0.2", "it-to-stream": "^1.0.0", "merge-options": "^3.0.4", "multiaddr": "^10.0.0", "multiaddr-to-uri": "^8.0.0", "multiformats": "^9.5.1", "nanoid": "^3.1.23", "parse-duration": "^1.0.0", "timeout-abort-controller": "^3.0.0", "uint8arrays": "^3.0.0"}}, "node_modules/ipfs-http-client": {"version": "56.0.3", "resolved": "https://registry.npmjs.org/ipfs-http-client/-/ipfs-http-client-56.0.3.tgz", "integrity": "sha512-E3L5ylVl6BjyRUsNehvfuRBYp1hj8vQ8in4zskVPMNzXs6JiCFUbif5a6BtcAlSK4xPQyJCeLNNAWLUeFQTLNA==", "deprecated": "js-IPFS has been deprecated in favour of Helia - please see https://github.com/ipfs/js-ipfs/issues/4336 for details", "dependencies": {"@ipld/dag-cbor": "^7.0.0", "@ipld/dag-json": "^8.0.1", "@ipld/dag-pb": "^2.1.3", "any-signal": "^3.0.0", "dag-jose": "^1.0.0", "debug": "^4.1.1", "err-code": "^3.0.1", "ipfs-core-types": "^0.10.3", "ipfs-core-utils": "^0.14.3", "ipfs-utils": "^9.0.6", "it-first": "^1.0.6", "it-last": "^1.0.4", "merge-options": "^3.0.4", "multiaddr": "^10.0.0", "multiformats": "^9.5.1", "parse-duration": "^1.0.0", "stream-to-it": "^0.2.2", "uint8arrays": "^3.0.0"}, "engines": {"node": ">=15.0.0", "npm": ">=3.0.0"}}, "node_modules/ipfs-unixfs": {"version": "6.0.9", "resolved": "https://registry.npmjs.org/ipfs-unixfs/-/ipfs-unixfs-6.0.9.tgz", "integrity": "sha512-0DQ7p0/9dRB6XCb0mVCTli33GzIzSVx5udpJuVM47tGcD+W+Bl4LsnoLswd3ggNnNEakMv1FdoFITiEnchXDqQ==", "dependencies": {"err-code": "^3.0.1", "protobufjs": "^6.10.2"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/ipfs-unixfs-exporter": {"version": "13.6.2", "resolved": "https://registry.npmjs.org/ipfs-unixfs-exporter/-/ipfs-unixfs-exporter-13.6.2.tgz", "integrity": "sha512-U3NkQHvQn5XzxtjSo1/GfoFIoXYY4hPgOlZG5RUrV5ScBI222b3jAHbHksXZuMy7sqPkA9ieeWdOmnG1+0nxyw==", "dependencies": {"@ipld/dag-cbor": "^9.2.1", "@ipld/dag-json": "^10.2.2", "@ipld/dag-pb": "^4.1.2", "@multiformats/murmur3": "^2.1.8", "hamt-sharding": "^3.0.6", "interface-blockstore": "^5.3.0", "ipfs-unixfs": "^11.0.0", "it-filter": "^3.1.1", "it-last": "^3.0.6", "it-map": "^3.1.1", "it-parallel": "^3.0.8", "it-pipe": "^3.0.1", "it-pushable": "^3.2.3", "multiformats": "^13.2.3", "p-queue": "^8.0.1", "progress-events": "^1.0.1"}}, "node_modules/ipfs-unixfs-exporter/node_modules/@ipld/dag-cbor": {"version": "9.2.4", "resolved": "https://registry.npmjs.org/@ipld/dag-cbor/-/dag-cbor-9.2.4.tgz", "integrity": "sha512-GbDWYl2fdJgkYtIJN0HY9oO0o50d1nB4EQb7uYWKUd2ztxCjxiEW3PjwGG0nqUpN1G4Cug6LX8NzbA7fKT+zfA==", "dependencies": {"cborg": "^4.0.0", "multiformats": "^13.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/ipfs-unixfs-exporter/node_modules/@ipld/dag-json": {"version": "10.2.5", "resolved": "https://registry.npmjs.org/@ipld/dag-json/-/dag-json-10.2.5.tgz", "integrity": "sha512-Q4Fr3IBDEN8gkpgNefynJ4U/ZO5Kwr7WSUMBDbZx0c37t0+IwQCTM9yJh8l5L4SRFjm31MuHwniZ/kM+P7GQ3Q==", "dependencies": {"cborg": "^4.0.0", "multiformats": "^13.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/ipfs-unixfs-exporter/node_modules/@ipld/dag-pb": {"version": "4.1.5", "resolved": "https://registry.npmjs.org/@ipld/dag-pb/-/dag-pb-4.1.5.tgz", "integrity": "sha512-w4PZ2yPqvNmlAir7/2hsCRMqny1EY5jj26iZcSgxREJexmbAc2FI21jp26MqiNdfgAxvkCnf2N/TJI18GaDNwA==", "dependencies": {"multiformats": "^13.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/ipfs-unixfs-exporter/node_modules/cborg": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/cborg/-/cborg-4.2.11.tgz", "integrity": "sha512-7gs3iaqtsD9OHowgqzc6ixQGwSBONqosVR2co0Bg0pARgrLap+LCcEIXJuuIz2jHy0WWQeDMFPEsU2r17I2XPQ==", "bin": {"cborg": "lib/bin.js"}}, "node_modules/ipfs-unixfs-exporter/node_modules/ipfs-unixfs": {"version": "11.2.1", "resolved": "https://registry.npmjs.org/ipfs-unixfs/-/ipfs-unixfs-11.2.1.tgz", "integrity": "sha512-gUeeX63EFgiaMgcs0cUs2ZUPvlOeEZ38okjK8twdWGZX2jYd2rCk8k/TJ3DSRIDZ2t/aZMv6I23guxHaofZE3w==", "dependencies": {"protons-runtime": "^5.5.0", "uint8arraylist": "^2.4.8"}}, "node_modules/ipfs-unixfs-exporter/node_modules/it-last": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-last/-/it-last-3.0.8.tgz", "integrity": "sha512-sdzoMeMAIJmRucZTnRd1GTtcoGV2EAS81fXfRKCVLviEX1wcvHhE43G0b/aKFFPc6ypuHWZR8vxaoHtDz/6b/A=="}, "node_modules/ipfs-unixfs-exporter/node_modules/it-map": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/it-map/-/it-map-3.1.3.tgz", "integrity": "sha512-BAdTuPN/Ie5K4pKLShqyLGBvkLSPtraYXBrX8h+Ki1CZQI8o0dOcaLewISLTXmEJsOHcAjkwxJsVwxND4/Rkpg==", "dependencies": {"it-peekable": "^3.0.0"}}, "node_modules/ipfs-unixfs-exporter/node_modules/it-peekable": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/it-peekable/-/it-peekable-3.0.7.tgz", "integrity": "sha512-w9W0WzNCsHLctV0z6vAA6N3jPgJu0qZZVlhngS+L29Rdva940f4Ea4ubtEXXYVBbq3l9Woo1MdWLGiEXzQDtdg=="}, "node_modules/ipfs-unixfs-exporter/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/ipfs-unixfs-importer": {"version": "15.3.2", "resolved": "https://registry.npmjs.org/ipfs-unixfs-importer/-/ipfs-unixfs-importer-15.3.2.tgz", "integrity": "sha512-12FqAAAE3YC6AHtYxZ944nDCabmvbNLdhNCVIN5RJIOri82ss62XdX4lsLpex9VvPzDIJyTAsrKJPcwM6hXGdQ==", "dependencies": {"@ipld/dag-pb": "^4.1.2", "@multiformats/murmur3": "^2.1.8", "hamt-sharding": "^3.0.6", "interface-blockstore": "^5.3.0", "interface-store": "^6.0.0", "ipfs-unixfs": "^11.0.0", "it-all": "^3.0.6", "it-batch": "^3.0.6", "it-first": "^3.0.6", "it-parallel-batch": "^3.0.6", "multiformats": "^13.2.3", "progress-events": "^1.0.1", "rabin-wasm": "^0.1.5", "uint8arraylist": "^2.4.8", "uint8arrays": "^5.1.0"}}, "node_modules/ipfs-unixfs-importer/node_modules/@ipld/dag-pb": {"version": "4.1.5", "resolved": "https://registry.npmjs.org/@ipld/dag-pb/-/dag-pb-4.1.5.tgz", "integrity": "sha512-w4PZ2yPqvNmlAir7/2hsCRMqny1EY5jj26iZcSgxREJexmbAc2FI21jp26MqiNdfgAxvkCnf2N/TJI18GaDNwA==", "dependencies": {"multiformats": "^13.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/ipfs-unixfs-importer/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/ipfs-unixfs-importer/node_modules/ipfs-unixfs": {"version": "11.2.1", "resolved": "https://registry.npmjs.org/ipfs-unixfs/-/ipfs-unixfs-11.2.1.tgz", "integrity": "sha512-gUeeX63EFgiaMgcs0cUs2ZUPvlOeEZ38okjK8twdWGZX2jYd2rCk8k/TJ3DSRIDZ2t/aZMv6I23guxHaofZE3w==", "dependencies": {"protons-runtime": "^5.5.0", "uint8arraylist": "^2.4.8"}}, "node_modules/ipfs-unixfs-importer/node_modules/it-all": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-all/-/it-all-3.0.8.tgz", "integrity": "sha512-TFAXqUjwuPFhyktbU7XIOjdvqjpc/c2xvDYfCrfHA6HP68+EQDCXuwGJ9YchvZTyXSaB2fkX3lI9aybcFUHWUw=="}, "node_modules/ipfs-unixfs-importer/node_modules/it-first": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-first/-/it-first-3.0.8.tgz", "integrity": "sha512-neaRRwOMCmMKkXJVZ4bvUDVlde+Xh0aTWr7hFaOZeDXzbctGVV/WHmPVqBqy3RjlsP7eRM0vcqNtlM8hivcmGw=="}, "node_modules/ipfs-unixfs-importer/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/ipfs-unixfs-importer/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/ipfs-utils": {"version": "9.0.14", "resolved": "https://registry.npmjs.org/ipfs-utils/-/ipfs-utils-9.0.14.tgz", "integrity": "sha512-zIaiEGX18QATxgaS0/EOQNoo33W0islREABAcxXE8n7y2MGAlB+hdsxXn4J0hGZge8IqVQhW8sWIb+oJz2yEvg==", "dependencies": {"any-signal": "^3.0.0", "browser-readablestream-to-it": "^1.0.0", "buffer": "^6.0.1", "electron-fetch": "^1.7.2", "err-code": "^3.0.1", "is-electron": "^2.2.0", "iso-url": "^1.1.5", "it-all": "^1.0.4", "it-glob": "^1.0.1", "it-to-stream": "^1.0.0", "merge-options": "^3.0.4", "nanoid": "^3.1.20", "native-fetch": "^3.0.0", "node-fetch": "^2.6.8", "react-native-fetch-api": "^3.0.0", "stream-to-it": "^0.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/ipns": {"version": "10.0.2", "resolved": "https://registry.npmjs.org/ipns/-/ipns-10.0.2.tgz", "integrity": "sha512-tokCgz9X678zvHnAabVG91K64X7HnHdWOrop0ghUcXkzH5XNsmxHwVpqVATNqq/w62h7fRDhWURHU/WOfYmCpA==", "dependencies": {"@libp2p/crypto": "^5.0.0", "@libp2p/interface": "^2.0.0", "@libp2p/logger": "^5.0.0", "cborg": "^4.2.3", "interface-datastore": "^8.3.0", "multiformats": "^13.2.2", "protons-runtime": "^5.5.0", "timestamp-nano": "^1.0.1", "uint8arraylist": "^2.4.8", "uint8arrays": "^5.1.0"}}, "node_modules/ipns/node_modules/cborg": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/cborg/-/cborg-4.2.11.tgz", "integrity": "sha512-7gs3iaqtsD9OHowgqzc6ixQGwSBONqosVR2co0Bg0pARgrLap+LCcEIXJuuIz2jHy0WWQeDMFPEsU2r17I2XPQ==", "bin": {"cborg": "lib/bin.js"}}, "node_modules/ipns/node_modules/interface-datastore": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/interface-datastore/-/interface-datastore-8.3.2.tgz", "integrity": "sha512-R3NLts7pRbJKc3qFdQf+u40hK8XWc0w4Qkx3OFEstC80VoaDUABY/dXA2EJPhtNC+bsrf41Ehvqb6+pnIclyRA==", "dependencies": {"interface-store": "^6.0.0", "uint8arrays": "^5.1.0"}}, "node_modules/ipns/node_modules/interface-store": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/interface-store/-/interface-store-6.0.3.tgz", "integrity": "sha512-+WvfEZnFUhRwFxgz+QCQi7UC6o9AM0EHM9bpIe2Nhqb100NHCsTvNAn4eJgvgV2/tmLo1MP9nGxQKEcZTAueLA=="}, "node_modules/ipns/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/ipns/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/is-electron": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/is-electron/-/is-electron-2.2.2.tgz", "integrity": "sha512-FO/Rhvz5tuw4MCWkpMzHFKWD2LsfHzIb7i6MdPYZ/KW7AlxawyLkqdy+jPZP1WubqEADE3O4FUENlJHDfQASRg=="}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-ip": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-ip/-/is-ip-3.1.0.tgz", "integrity": "sha512-35vd5necO7IitFPjd/YBeqwWnyDWbuLH9ZXQdMfDA8TEo7pv5X8yfrvVO3xbJbLUlERCMvf6X0hTUamQxCYJ9Q==", "dependencies": {"ip-regex": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-loopback-addr": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/is-loopback-addr/-/is-loopback-addr-2.0.2.tgz", "integrity": "sha512-26POf2KRCno/KTNL5Q0b/9TYnL00xEsSaLfiFRmjM7m7Lw7ZMmFybzzuX4CcsLAluZGd+niLUiMRxEooVE3aqg=="}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "engines": {"node": ">=0.12.0"}}, "node_modules/is-plain-obj": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-2.1.0.tgz", "integrity": "sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==", "engines": {"node": ">=8"}}, "node_modules/is-regexp": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/is-regexp/-/is-regexp-3.1.0.tgz", "integrity": "sha512-rbku49cWloU5bSMI+zaRaXdQHXnthP6DZ/vLnfdSKyL4zUzuWnomtOEiZZOd+ioQ+avFo/qau3KPTc7Fjy1uPA==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/iso-url": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/iso-url/-/iso-url-1.2.1.tgz", "integrity": "sha512-9JPDgCN4B7QPkLtYAAOrEuAWvP9rWvR5offAr0/SeF046wIkglqH3VXgYYP6NcsKslH80UIVgmPqNe3j7tG2ng==", "engines": {"node": ">=12"}}, "node_modules/it-all": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/it-all/-/it-all-1.0.6.tgz", "integrity": "sha512-3cmCc6Heqe3uWi3CVM/k51fa/XbMFpQVzFoDsV0IZNHSQDyAXl3c4MjHkFX5kF3922OGj7Myv1nSEUgRtcuM1A=="}, "node_modules/it-batch": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-batch/-/it-batch-3.0.8.tgz", "integrity": "sha512-sZPc1Wwtht3hzUcoPMTr5ByxPJ+y7ok3kAduxTslSnilGsR02aTNMc/wUrZlRHxyx6oWnjTrv1ASpSszguPumQ=="}, "node_modules/it-drain": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/it-drain/-/it-drain-3.0.9.tgz", "integrity": "sha512-HKy+UVYAqSFm+naEkNg14BwKymjHK0SxYLi8H5nACTIgbemDMZ4SNa2omzMUuk2Nu3jhaHMoqUJfZ0aBcdn4oA=="}, "node_modules/it-filter": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/it-filter/-/it-filter-3.1.3.tgz", "integrity": "sha512-jicHnWmWdRj9NpznADvidoc/9Vlte/Bv+bg/Amf/Zc3U8iovSQMPB0aZSqOSjXGe5KXQuau5poocOFyyiP+RTg==", "dependencies": {"it-peekable": "^3.0.0"}}, "node_modules/it-filter/node_modules/it-peekable": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/it-peekable/-/it-peekable-3.0.7.tgz", "integrity": "sha512-w9W0WzNCsHLctV0z6vAA6N3jPgJu0qZZVlhngS+L29Rdva940f4Ea4ubtEXXYVBbq3l9Woo1MdWLGiEXzQDtdg=="}, "node_modules/it-first": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/it-first/-/it-first-1.0.7.tgz", "integrity": "sha512-nvJKZoBpZD/6Rtde6FXqwDqDZGF1sCADmr2Zoc0hZsIvnE449gRFnGctxDf09Bzc/FWnHXAdaHVIetY6lrE0/g=="}, "node_modules/it-foreach": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/it-foreach/-/it-foreach-2.1.3.tgz", "integrity": "sha512-QfrD0Sjv0Uy664huiZevAgY1UEsJ1GlmPpjwy38vjSi4rCmdGkO7ef/KKG86ZXd9j+j1bXXGnfDLjCs7lU8A0A==", "dependencies": {"it-peekable": "^3.0.0"}}, "node_modules/it-foreach/node_modules/it-peekable": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/it-peekable/-/it-peekable-3.0.7.tgz", "integrity": "sha512-w9W0WzNCsHLctV0z6vAA6N3jPgJu0qZZVlhngS+L29Rdva940f4Ea4ubtEXXYVBbq3l9Woo1MdWLGiEXzQDtdg=="}, "node_modules/it-glob": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/it-glob/-/it-glob-1.0.2.tgz", "integrity": "sha512-Ch2Dzhw4URfB9L/0ZHyY+uqOnKvBNeS/SMcRiPmJfpHiM0TsUZn+GkpcZxAoF3dJVdPm/PuIk3A4wlV7SUo23Q==", "dependencies": {"@types/minimatch": "^3.0.4", "minimatch": "^3.0.4"}}, "node_modules/it-last": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/it-last/-/it-last-1.0.6.tgz", "integrity": "sha512-aFGeibeiX/lM4bX3JY0OkVCFkAw8+n9lkukkLNivbJRvNz8lI3YXv5xcqhFUV2lDJiraEK3OXRDbGuevnnR67Q=="}, "node_modules/it-length-prefixed": {"version": "10.0.1", "resolved": "https://registry.npmjs.org/it-length-prefixed/-/it-length-prefixed-10.0.1.tgz", "integrity": "sha512-BhyluvGps26u9a7eQIpOI1YN7mFgi8lFwmiPi07whewbBARKAG9LE09Odc8s1Wtbt2MB6rNUrl7j9vvfXTJwdQ==", "dependencies": {"it-reader": "^6.0.1", "it-stream-types": "^2.0.1", "uint8-varint": "^2.0.1", "uint8arraylist": "^2.0.0", "uint8arrays": "^5.0.1"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/it-length-prefixed/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/it-length-prefixed/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/it-map": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/it-map/-/it-map-1.0.6.tgz", "integrity": "sha512-XT4/RM6UHIFG9IobGlQPFQUrlEKkU4eBUFG3qhWhfAdh1JfF2x11ShCrKCdmZ0OiZppPfoLuzcfA4cey6q3UAQ=="}, "node_modules/it-merge": {"version": "3.0.11", "resolved": "https://registry.npmjs.org/it-merge/-/it-merge-3.0.11.tgz", "integrity": "sha512-7Kzf/XN1jFlhXRfeDoHeBlgmMv/zOv+ji2LXEN6hsIlW2S/8PRjw+4s4dZbtFd+u5Pk7li+2Hd+a/NHwsqT0iQ==", "dependencies": {"it-queueless-pushable": "^2.0.0"}}, "node_modules/it-ndjson": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/it-ndjson/-/it-ndjson-1.1.3.tgz", "integrity": "sha512-HQTTCY1mi651Qz6Ijss1GtammSnPSN+MXzpXcD6RvytG2xJlMxD95RlKQP5JAE8jZtfSugGRUf9geXai7DuNBQ==", "dependencies": {"uint8arraylist": "^2.4.8"}}, "node_modules/it-parallel": {"version": "3.0.11", "resolved": "https://registry.npmjs.org/it-parallel/-/it-parallel-3.0.11.tgz", "integrity": "sha512-ABHAwLO6RMB9zBKUN1v7pJWupwGaMkUrtGNnygDqog5yB8PjyKWxUKLwca1OHuZrdnkOx0VzETEXMSzWrzX8bw==", "dependencies": {"p-defer": "^4.0.1"}}, "node_modules/it-parallel-batch": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-parallel-batch/-/it-parallel-batch-3.0.8.tgz", "integrity": "sha512-FWKdzfPNfUgE7DdHWPGPfRHXhFkx427F5rKHCM1DpN0ALsMdi/lhVOIhI7kNHoQwzYz/bFpPLCCA8BgnOcznXA==", "dependencies": {"it-batch": "^3.0.0"}}, "node_modules/it-parallel/node_modules/p-defer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-4.0.1.tgz", "integrity": "sha512-Mr5KC5efvAK5VUptYEIopP1bakB85k2IWXaRC0rsh1uwn1L6M0LVml8OIQ4Gudg4oyZakf7FmeRLkMMtZW1i5A==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/it-peekable": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/it-peekable/-/it-peekable-1.0.3.tgz", "integrity": "sha512-5+8zemFS+wSfIkSZyf0Zh5kNN+iGyccN02914BY4w/Dj+uoFEoPSvj5vaWn8pNZJNSxzjW0zHRxC3LUb2KWJTQ=="}, "node_modules/it-pipe": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/it-pipe/-/it-pipe-3.0.1.tgz", "integrity": "sha512-sIoNrQl1qSRg2seYSBH/3QxWhJFn9PKYvOf/bHdtCBF0bnghey44VyASsWzn5dAx0DCDDABq1hZIuzKmtBZmKA==", "dependencies": {"it-merge": "^3.0.0", "it-pushable": "^3.1.2", "it-stream-types": "^2.0.1"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/it-pushable": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/it-pushable/-/it-pushable-3.2.3.tgz", "integrity": "sha512-gzYnXYK8Y5t5b/BnJUr7glfQLO4U5vyb05gPx/TyTw+4Bv1zM9gFk4YsOrnulWefMewlphCjKkakFvj1y99Tcg==", "dependencies": {"p-defer": "^4.0.0"}}, "node_modules/it-pushable/node_modules/p-defer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-4.0.1.tgz", "integrity": "sha512-Mr5KC5efvAK5VUptYEIopP1bakB85k2IWXaRC0rsh1uwn1L6M0LVml8OIQ4Gudg4oyZakf7FmeRLkMMtZW1i5A==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/it-queueless-pushable": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/it-queueless-pushable/-/it-queueless-pushable-2.0.1.tgz", "integrity": "sha512-ZFX4ZHpzPwD0Ivpt3y98FtXk/KToO0ec5AjUDBQ4MOdkWVmnjEBfjq6ncvpuGtZ3776KSSK+i6uWkrSspdo/OQ==", "dependencies": {"abort-error": "^1.0.1", "p-defer": "^4.0.1", "race-signal": "^1.1.3"}}, "node_modules/it-queueless-pushable/node_modules/p-defer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-4.0.1.tgz", "integrity": "sha512-Mr5KC5efvAK5VUptYEIopP1bakB85k2IWXaRC0rsh1uwn1L6M0LVml8OIQ4Gudg4oyZakf7FmeRLkMMtZW1i5A==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/it-reader": {"version": "6.0.4", "resolved": "https://registry.npmjs.org/it-reader/-/it-reader-6.0.4.tgz", "integrity": "sha512-XCWifEcNFFjjBHtor4Sfaj8rcpt+FkY0L6WdhD578SCDhV4VUm7fCkF3dv5a+fTcfQqvN9BsxBTvWbYO6iCjTg==", "dependencies": {"it-stream-types": "^2.0.1", "uint8arraylist": "^2.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/it-sort": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-sort/-/it-sort-3.0.8.tgz", "integrity": "sha512-RP0jzwaG9vrCFEpBNW8INOJ0Z2rv+2YXZ2jFICJbtP9BXL/qLWJ/TCgOF3+4hPCJK8g2czDtA55K9DuAJ+Ghsg==", "dependencies": {"it-all": "^3.0.0"}}, "node_modules/it-sort/node_modules/it-all": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-all/-/it-all-3.0.8.tgz", "integrity": "sha512-TFAXqUjwuPFhyktbU7XIOjdvqjpc/c2xvDYfCrfHA6HP68+EQDCXuwGJ9YchvZTyXSaB2fkX3lI9aybcFUHWUw=="}, "node_modules/it-stream-types": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/it-stream-types/-/it-stream-types-2.0.2.tgz", "integrity": "sha512-Rz/DEZ6Byn/r9+/SBCuJhpPATDF9D+dz5pbgSUyBsCDtza6wtNATrz/jz1gDyNanC3XdLboriHnOC925bZRBww=="}, "node_modules/it-take": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/it-take/-/it-take-3.0.8.tgz", "integrity": "sha512-avNX5LYv+eUh80eOgCAB+Drg9m1qTt06hpC5w0hc/AVaCFzG5FIJ8dAzPYVzQoq5p7l7hXg1Inoj2RTHJRqCGA=="}, "node_modules/it-to-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/it-to-stream/-/it-to-stream-1.0.0.tgz", "integrity": "sha512-pLULMZMAB/+vbdvbZtebC0nWBTbG581lk6w8P7DfIIIKUfa8FbY7Oi0FxZcFPbxvISs7A9E+cMpLDBc1XhpAOA==", "dependencies": {"buffer": "^6.0.3", "fast-fifo": "^1.0.0", "get-iterator": "^1.0.2", "p-defer": "^3.0.0", "p-fifo": "^1.0.0", "readable-stream": "^3.6.0"}}, "node_modules/long": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/long/-/long-4.0.0.tgz", "integrity": "sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA=="}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "engines": {"node": ">= 0.4"}}, "node_modules/merge-options": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/merge-options/-/merge-options-3.0.4.tgz", "integrity": "sha512-2Sug1+knBjkaMsMgf1ctR1Ujx+Ayku4EdJN4Z+C2+JzoeF7A3OZ9KM2GY0CpQS51NR61LTurMJrRKPhSs3ZRTQ==", "dependencies": {"is-plain-obj": "^2.1.0"}, "engines": {"node": ">=10"}}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mortice": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/mortice/-/mortice-3.1.0.tgz", "integrity": "sha512-9IpFWfzFlVVj3W+u6+iTTSu/UETdGVHiSxc9GxGWdE97SSmwzexw1H5usb/6lYG3C7Y+YizpDlMN4onilItOxg==", "dependencies": {"abort-error": "^1.0.0", "observable-webworkers": "^2.0.1", "p-queue": "^8.0.1"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/multiaddr": {"version": "10.0.1", "resolved": "https://registry.npmjs.org/multiaddr/-/multiaddr-10.0.1.tgz", "integrity": "sha512-G5upNcGzEGuTHkzxezPrrD6CaIHR9uo+7MwqhNVcXTs33IInon4y7nMiGxl2CY5hG7chvYQUQhz5V52/Qe3cbg==", "deprecated": "This module is deprecated, please upgrade to @multiformats/multiaddr", "dependencies": {"dns-over-http-resolver": "^1.2.3", "err-code": "^3.0.1", "is-ip": "^3.1.0", "multiformats": "^9.4.5", "uint8arrays": "^3.0.0", "varint": "^6.0.0"}}, "node_modules/multiaddr-to-uri": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/multiaddr-to-uri/-/multiaddr-to-uri-8.0.0.tgz", "integrity": "sha512-dq4p/vsOOUdVEd1J1gl+R2GFrXJQH8yjLtz4hodqdVbieg39LvBOdMQRdQnfbg5LSM/q1BYNVf5CBbwZFFqBgA==", "deprecated": "This module is deprecated, please upgrade to @multiformats/multiaddr-to-uri", "dependencies": {"multiaddr": "^10.0.0"}}, "node_modules/multiformats": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-9.9.0.tgz", "integrity": "sha512-HoMUjhH9T8DDBNT+6xzkrd9ga/XiBI4xLr58LJACwK6G3HTOPeMz4nB4KJs33L2BelrIJa7P0VuNaVF3hMYfjg=="}, "node_modules/murmurhash3js-revisited": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/murmurhash3js-revisited/-/murmurhash3js-revisited-3.0.0.tgz", "integrity": "sha512-/sF3ee6zvScXMb1XFJ8gDsSnY+X8PbOyjIuBhtgis10W2Jx4ZjIhikUCIF9c4gpJxVnQIsPAFrSwTCuAjicP6g==", "engines": {"node": ">=8.0.0"}}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/native-fetch": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/native-fetch/-/native-fetch-3.0.0.tgz", "integrity": "sha512-G3Z7vx0IFb/FQ4JxvtqGABsOTIqRWvgQz6e+erkB+JJD6LrszQtMozEHI4EkmgZQvnGHrpLVzUWk7t4sJCIkVw==", "peerDependencies": {"node-fetch": "*"}}, "node_modules/netmask": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/netmask/-/netmask-2.0.2.tgz", "integrity": "sha512-dBpDMdxv9Irdq66304OLfEmQ9tbNRFnFTuZiLo+bD+r332bBmMJ8GBLXklIXXgxd3+v9+KUnZaUR5PJMa75Gsg==", "engines": {"node": ">= 0.4.0"}}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/observable-webworkers": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/observable-webworkers/-/observable-webworkers-2.0.1.tgz", "integrity": "sha512-JI1vB0u3pZjoQKOK1ROWzp0ygxSi7Yb0iR+7UNsw4/Zn4cQ0P3R7XL38zac/Dy2tEA7Lg88/wIJTjF8vYXZ0uw==", "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}, "node_modules/p-defer": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/p-defer/-/p-defer-3.0.0.tgz", "integrity": "sha512-ugZxsxmtTln604yeYd29EGrNhazN2lywetzpKhfmQjW/VJmhpDmWbiX+h0zL8V91R0UXkhb3KtPmyq9PZw3aYw==", "engines": {"node": ">=8"}}, "node_modules/p-fifo": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/p-fifo/-/p-fifo-1.0.0.tgz", "integrity": "sha512-IjoCxXW48tqdtDFz6fqo5q1UfFVjjVZe8TC1QRflvNUJtNfCUhxOUw6MOVZhDPjqhSzc26xKdugsO17gmzd5+A==", "dependencies": {"fast-fifo": "^1.0.0", "p-defer": "^3.0.0"}}, "node_modules/p-queue": {"version": "8.1.0", "resolved": "https://registry.npmjs.org/p-queue/-/p-queue-8.1.0.tgz", "integrity": "sha512-mxLDbbGIBEXTJL0zEx8JIylaj3xQ7Z/7eEVjcF9fJX4DBiH9oqe+oahYnlKKxm0Ci9TlWTyhSHgygxMxjIB2jw==", "dependencies": {"eventemitter3": "^5.0.1", "p-timeout": "^6.1.2"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-timeout": {"version": "6.1.4", "resolved": "https://registry.npmjs.org/p-timeout/-/p-timeout-6.1.4.tgz", "integrity": "sha512-MyIV3ZA/PmyBN/ud8vV9XzwTrNtR4jFrObymZYnZqMmW0zA8Z17vnT0rBgFE/TlohB+YCHqXMgZzb3Csp49vqg==", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse-duration": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/parse-duration/-/parse-duration-1.1.2.tgz", "integrity": "sha512-p8EIONG8L0u7f8GFgfVlL4n8rnChTt8O5FSxgxMz2tjc9FMP199wxVKVB6IbKx11uTbKHACSvaLVIKNnoeNR/A=="}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/progress-events": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/progress-events/-/progress-events-1.0.1.tgz", "integrity": "sha512-MOzLIwhpt64KIVN64h1MwdKWiyKFNc/S6BoYKPIVUHFg0/eIEyBulhWCgn678v/4c0ri3FdGuzXymNCv02MUIw=="}, "node_modules/protobufjs": {"version": "6.11.4", "resolved": "https://registry.npmjs.org/protobufjs/-/protobufjs-6.11.4.tgz", "integrity": "sha512-5kQWPaJHi1WoCpjTGszzQ32PG2F4+wRY6BmAT4Vfw56Q2FZ4YZzK20xUYQH4YkfehY1e6QSICrJquM6xXZNcrw==", "hasInstallScript": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.1", "@types/node": ">=13.7.0", "long": "^4.0.0"}, "bin": {"pbjs": "bin/pbjs", "pbts": "bin/pbts"}}, "node_modules/protons-runtime": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/protons-runtime/-/protons-runtime-5.5.0.tgz", "integrity": "sha512-EsALjF9QsrEk6gbCx3lmfHxVN0ah7nG3cY7GySD4xf4g8cr7g543zB88Foh897Sr1RQJ9yDCUsoT1i1H/cVUFA==", "dependencies": {"uint8-varint": "^2.0.2", "uint8arraylist": "^2.4.3", "uint8arrays": "^5.0.1"}}, "node_modules/protons-runtime/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/protons-runtime/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/rabin-wasm": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/rabin-wasm/-/rabin-wasm-0.1.5.tgz", "integrity": "sha512-uWgQTo7pim1Rnj5TuWcCewRDTf0PEFTSlaUjWP4eY9EbLV9em08v89oCz/WO+wRxpYuO36XEHp4wgYQnAgOHzA==", "dependencies": {"@assemblyscript/loader": "^0.9.4", "bl": "^5.0.0", "debug": "^4.3.1", "minimist": "^1.2.5", "node-fetch": "^2.6.1", "readable-stream": "^3.6.0"}, "bin": {"rabin-wasm": "cli/bin.js"}}, "node_modules/race-event": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/race-event/-/race-event-1.3.0.tgz", "integrity": "sha512-kaLm7axfOnahIqD3jQ4l1e471FIFcEGebXEnhxyLscuUzV8C94xVHtWEqDDXxll7+yu/6lW0w1Ff4HbtvHvOHg=="}, "node_modules/race-signal": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/race-signal/-/race-signal-1.1.3.tgz", "integrity": "sha512-Mt2NznMgepLfORijhQMncE26IhkmjEphig+/1fKC0OtaKwys/gpvpmswSjoN01SS+VO951mj0L4VIDXdXsjnfA=="}, "node_modules/react-native-fetch-api": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/react-native-fetch-api/-/react-native-fetch-api-3.0.0.tgz", "integrity": "sha512-g2rtqPjdroaboDKTsJCTlcmtw54E25OjyaunUP0anOZn4Fuo2IKs8BVfe02zVggA/UysbmfSnRJIqtNkAgggNA==", "dependencies": {"p-defer": "^3.0.0"}}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/receptacle": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/receptacle/-/receptacle-1.3.2.tgz", "integrity": "sha512-HrsFvqZZheusncQRiEE7GatOAETrARKV/lnfYicIm8lbvp/JQOdADOfhjBd2DajvoszEyxSM6RlAAIZgEoeu/A==", "dependencies": {"ms": "^2.1.1"}}, "node_modules/retimer": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/retimer/-/retimer-3.0.0.tgz", "integrity": "sha512-WKE0j11Pa0ZJI5YIk0nflGI7SQsfl2ljihVy7ogh7DeQSeYAUi0ubZ/yEueGtDfUPk6GH5LRw1hBdLq4IwUBWA=="}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz", "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "node_modules/sparse-array": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/sparse-array/-/sparse-array-1.3.2.tgz", "integrity": "sha512-ZT711fePGn3+kQyLuv1fpd3rNSkNF8vd5Kv2D+qnOANeyKs3fx6bUMGWRPvgTTcYV64QMqZKZwcuaQSP3AZ0tg=="}, "node_modules/stream-to-it": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/stream-to-it/-/stream-to-it-0.2.4.tgz", "integrity": "sha512-4vEbkSs83OahpmBybNJXlJd7d6/RxzkkSdT3I0mnGt79Xd2Kk+e1JqbvAvsQfCeKj3aKb0QIWkyK3/n0j506vQ==", "dependencies": {"get-iterator": "^1.0.2"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/super-regex": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/super-regex/-/super-regex-0.2.0.tgz", "integrity": "sha512-WZzIx3rC1CvbMDloLsVw0lkZVKJWbrkJ0k1ghKFmcnPrW1+jWbgTkTEWVtD9lMdmI4jZEz40+naBxl1dCUhXXw==", "dependencies": {"clone-regexp": "^3.0.0", "function-timeout": "^0.1.0", "time-span": "^5.1.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-9.4.0.tgz", "integrity": "sha512-VL+lNrEoIXww1coLPOmiEmK/0sGigko5COxI09KzHc2VJXJsQ37UaQ+8quuxjDeA7+KnLGTWRyOXSLLR2Wb4jw==", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/time-span": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/time-span/-/time-span-5.1.0.tgz", "integrity": "sha512-75voc/9G4rDIJleOo4jPvN4/YC4GRZrY8yy1uU4lwrB3XEQbWve8zXoO5No4eFrGcTAMYyoY67p8jRQdtA1HbA==", "dependencies": {"convert-hrtime": "^5.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/timeout-abort-controller": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/timeout-abort-controller/-/timeout-abort-controller-3.0.0.tgz", "integrity": "sha512-O3e+2B8BKrQxU2YRyEjC/2yFdb33slI22WRdUaDx6rvysfi9anloNZyR2q0l6LnePo5qH7gSM7uZtvvwZbc2yA==", "dependencies": {"retimer": "^3.0.0"}}, "node_modules/timestamp-nano": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/timestamp-nano/-/timestamp-nano-1.0.1.tgz", "integrity": "sha512-4oGOVZWTu5sl89PtCDnhQBSt7/vL1zVEwAfxH1p49JhTosxzVQWYBYFRFZ8nJmo0G6f824iyP/44BFAwIoKvIA==", "engines": {"node": ">= 4.5.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw=="}, "node_modules/uint8-varint": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/uint8-varint/-/uint8-varint-2.0.4.tgz", "integrity": "sha512-FwpTa7ZGA/f/EssWAb5/YV6pHgVF1fViKdW8cWaEarjB8t7NyofSWBdOTyFPaGuUG4gx3v1O3PQ8etsiOs3lcw==", "dependencies": {"uint8arraylist": "^2.0.0", "uint8arrays": "^5.0.0"}}, "node_modules/uint8-varint/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/uint8-varint/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/uint8arraylist": {"version": "2.4.8", "resolved": "https://registry.npmjs.org/uint8arraylist/-/uint8arraylist-2.4.8.tgz", "integrity": "sha512-vc1PlGOzglLF0eae1M8mLRTBivsvrGsdmJ5RbK3e+QRvRLOZfZhQROTwH/OfyF3+ZVUg9/8hE8bmKP2CvP9quQ==", "dependencies": {"uint8arrays": "^5.0.1"}}, "node_modules/uint8arraylist/node_modules/multiformats": {"version": "13.3.6", "resolved": "https://registry.npmjs.org/multiformats/-/multiformats-13.3.6.tgz", "integrity": "sha512-yakbt9cPYj8d3vi/8o/XWm61MrOILo7fsTL0qxNx6zS0Nso6K5JqqS2WV7vK/KSuDBvrW3KfCwAdAgarAgOmww=="}, "node_modules/uint8arraylist/node_modules/uint8arrays": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-5.1.0.tgz", "integrity": "sha512-vA6nFepEmlSKkMBnLBaUMVvAC4G3CTmO58C12y4sq6WPDOR7mOFYOi7GlrQ4djeSbP6JG9Pv9tJDM97PedRSww==", "dependencies": {"multiformats": "^13.0.0"}}, "node_modules/uint8arrays": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/uint8arrays/-/uint8arrays-3.1.1.tgz", "integrity": "sha512-+QJa8QRnbdXVpHYjLoTpJIdCTiw9Ir62nocClWuXIq2JIh4Uta0cQsTSpFL678p2CN8B+XSApwcU+pQEqVpKWg==", "dependencies": {"multiformats": "^9.4.2"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ=="}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="}, "node_modules/varint": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/varint/-/varint-6.0.0.tgz", "integrity": "sha512-cXEIW6cfr15lFv563k4GuVuW/fiwjknytD37jIOLSdSWuOI6WnO/oKwmP2FQTU2l01LP8/M5TSAJpzUaGe3uWg=="}, "node_modules/weald": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/weald/-/weald-1.0.4.tgz", "integrity": "sha512-+kYTuHonJBwmFhP1Z4YQK/dGi3jAnJGCYhyODFpHK73rbxnp9lnZQj7a2m+WVgn8fXr5bJaxUpF6l8qZpPeNWQ==", "dependencies": {"ms": "^3.0.0-canary.1", "supports-color": "^9.4.0"}}, "node_modules/weald/node_modules/ms": {"version": "3.0.0-canary.1", "resolved": "https://registry.npmjs.org/ms/-/ms-3.0.0-canary.1.tgz", "integrity": "sha512-kh8ARjh8rMN7Du2igDRO9QJnqCb2xYTJxyQYK7vJJS4TvLLmsbyhiKpSW+t+y26gyOyMd0riphX0GeWKU3ky5g==", "engines": {"node": ">=12.13"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}}}