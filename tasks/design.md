# 基于区块链的专利交易系统设计文档

## 1. 项目概述

### 1.1 系统简介
基于区块链的专利交易系统，采用Ganache本地私链作为核心存储，通过MetaMask钱包进行用户身份认证。专利相关文档通过IPFS分布式存储，业务数据完全上链存储。系统支持专利上传、审核、交易、维权等全流程管理，实现去中心化的专利交易生态。

### 1.2 技术架构
- **前端**: Vue.js 3 + Vite，集成MetaMask钱包
- **后端**: Node.js + Bootstrap，提供API服务和业务逻辑处理
- **区块链**: Ganache本地私链，存储所有业务数据
- **身份认证**: MetaMask钱包插件，无需传统注册登录
- **文件存储**: IPFS网络，存储专利文档和证明材料
- **智能合约**: Solidity，实现业务逻辑和数据管理

### 1.3 用户角色与权限
- **超级管理员**: 智能合约部署者，拥有系统最高管理权限，可进行用户管理、日志查看、账号管理
- **专利审核方**: 负责专利上传、交易、维权等事务的审核工作
- **普通用户**: 可进行专利上传、搜索、交易、维权等操作

### 1.4 认证机制
系统采用MetaMask钱包进行身份认证，用户通过连接MetaMask钱包获得区块链地址作为唯一标识。用户首次连接后可在系统内完善个人信息（姓名、手机号、身份证号），该信息存储在智能合约中并与区块链地址绑定。

## 2. 前端模块设计

### 2.1 MetaMask集成模块 (MetaMask Integration Module)

#### 2.1.1 钱包连接功能
- **功能描述**: 
  - 检测MetaMask钱包安装状态
  - 连接MetaMask钱包获取用户地址
  - 监听账户切换和网络变化
  - 验证当前网络是否为Ganache私链
- **组件**: `MetaMaskConnector.vue`
- **API接口**:
  ```javascript
  // 验证用户地址是否已注册
  GET /api/auth/check-user/:address
  
  // 获取用户基本信息
  GET /api/auth/user-info/:address
  ```

#### 2.1.2 网络验证
- **功能描述**: 
  - 验证用户是否连接到正确的Ganache网络
  - 网络切换提示和引导
  - 显示当前连接状态
- **组件**: `NetworkValidator.vue`

### 2.2 用户中心模块 (User Center Module)

#### 2.2.1 个人信息管理
- **路由**: `/profile`
- **功能描述**: 
  - 显示用户区块链地址（不可修改）
  - 填写/修改个人信息：姓名、手机号、身份证号
  - 首次登录用户必须完善个人信息
  - 信息修改需要区块链交易确认
- **组件**: `UserProfile.vue`
- **API接口**:
  ```javascript
  // 更新用户个人信息
  PUT /api/user/profile
  Body: {
    address: string,
    name: string,
    phone: string,
    idCard: string
  }
  
  // 获取用户个人信息
  GET /api/user/profile/:address
  ```

#### 2.2.2 钱包功能
- **路由**: `/wallet`
- **功能描述**: 
  - 显示钱包余额（从智能合约读取）
  - 模拟充值功能（调用智能合约增加余额）
  - 模拟提现功能（调用智能合约减少余额）
  - 交易流水记录展示（收入/支出、专利名称、时间）
- **组件**: 
  - `Wallet.vue` - 钱包主界面
  - `RechargeModal.vue` - 充值弹窗
  - `WithdrawModal.vue` - 提现弹窗
  - `TransactionHistory.vue` - 流水记录
- **API接口**:
  ```javascript
  // 获取钱包余额
  GET /api/wallet/balance/:address
  
  // 模拟充值
  POST /api/wallet/recharge
  Body: { address: string, amount: number }
  
  // 模拟提现
  POST /api/wallet/withdraw
  Body: { address: string, amount: number }
  
  // 获取交易流水
  GET /api/wallet/transactions/:address
  ```

### 2.3 专利管理模块 (Patent Management Module)

#### 2.3.1 专利上传
- **路由**: `/patent/upload`
- **功能描述**: 
  - 专利基本信息录入：专利名称、专利号、专利类别、转让价格、专利摘要、专利申请日期、专利权结束日期
  - 专利权人信息：专利权人姓名、专利权人身份证号
  - 代理出售选择：是否为代理出售（单选框）
  - 文档上传：如果是代理出售，上传专利代理委托证明文档；如果不是，上传专利权证明文档
  - 专利文档上传：专利的详细技术文档
  - 文件类型支持：Word、PDF、PNG等格式
  - 所有文档通过IPFS存储，返回哈希值保存到区块链
  - 上传成功后专利状态为"审核中"
- **组件**: `PatentUpload.vue`
- **API接口**:
  ```javascript
  // 上传文件到IPFS
  POST /api/ipfs/upload
  Body: FormData (支持多文件上传)
  Response: { hashes: string[], urls: string[] }
  
  // 提交专利信息
  POST /api/patents
  Body: {
    patentName: string,
    patentNumber: string,
    category: string,
    price: number,
    summary: string,
    applicationDate: string,
    expirationDate: string,
    ownerName: string,
    ownerIdCard: string,
    isAgency: boolean,
    documentHash: string,      // 专利文档IPFS哈希
    proofDocumentHash: string, // 证明文档IPFS哈希
    uploaderAddress: string
  }
  ```

#### 2.3.2 专利搜索
- **路由**: `/patent/search`
- **功能描述**: 
  - 支持按专利名称进行模糊搜索和精准搜索
  - 支持按专利号精确搜索
  - 支持按专利类别筛选搜索
  - 搜索结果展示：专利名称、专利号、类别、价格、状态、上传时间
  - 分页显示搜索结果
  - 点击专利可查看详细信息
- **组件**: 
  - `PatentSearch.vue` - 搜索表单
  - `PatentList.vue` - 搜索结果列表
  - `PatentCard.vue` - 专利卡片组件
- **API接口**:
  ```javascript
  // 搜索专利
  GET /api/patents/search
  Query: {
    keyword?: string,    // 专利名称关键词
    patentNumber?: string, // 专利号
    category?: string,   // 专利类别
    searchType?: 'fuzzy' | 'exact', // 搜索类型
    page?: number,
    limit?: number
  }
  
  // 获取专利类别列表
  GET /api/patents/categories
  ```

#### 2.3.3 专利详情页
- **路由**: `/patent/:id`
- **功能描述**: 
  - 显示专利完整信息：区块链信息、专利名称、专利号、转让价格、专利摘要、申请日期、权利结束日期
  - 显示专利上传人信息（可点击查看姓名、手机号）
  - 显示是否为代理出售
  - 提供文档查看和下载功能：专利文档、专利代理委托证明文档或专利权证明文档
  - 专利交易按钮（状态为"已通过"且非当前用户时可见）
  - 专利事务溯源展示
- **组件**: 
  - `PatentDetail.vue` - 专利详情主组件
  - `PatentDocuments.vue` - 文档查看下载组件
  - `PatentEvents.vue` - 专利事务溯源组件
  - `TradeButton.vue` - 交易按钮组件
- **API接口**:
  ```javascript
  // 获取专利详细信息
  GET /api/patents/:id
  
  // 获取专利事务记录
  GET /api/patents/:id/events
  
  // 获取IPFS文件
  GET /api/ipfs/:hash
  
  // 下载IPFS文件
  GET /api/ipfs/:hash/download
  ```

#### 2.3.4 我的专利管理
- **路由设计**:
  - `/my-patents/published` - 我发布的专利
  - `/my-patents/purchased` - 我购买的专利  
  - `/my-patents/owned` - 我的专利（当前拥有专利权的所有专利）

##### 我发布的专利
- **功能描述**:
  - 显示用户上传的所有专利，包括专利名称、专利号、发布时间
  - 显示专利状态：审核中、正常、已交易
  - 提供专利详细信息查看
  - 专利管理操作：撤销专利、专利冻结（不可交易）、专利恢复（可交易）
  - 已交易专利可查看交易详情和智能合约生成的交易报表

##### 我购买的专利
- **功能描述**:
  - 显示用户购买的所有专利
  - 显示交易信息：交易双方姓名、身份证号、区块链地址、审核员信息、交易成功时间
  - 提供智能合约生成的交易报表下载

##### 我的专利（当前拥有）
- **功能描述**:
  - 显示用户当前拥有专利权的所有专利（未售出的上传专利 + 购买的专利）
  - 专利可重新设置价格并开放二次销售
  - 显示专利基本信息和交易历史

- **组件**: 
  - `MyPatents.vue` - 专利管理主页面
  - `PatentStatusControl.vue` - 专利状态控制组件
  - `TradeContractModal.vue` - 交易合同查看弹窗
- **API接口**:
  ```javascript
  // 获取我发布的专利
  GET /api/patents/my-published/:address
  
  // 获取我购买的专利
  GET /api/patents/my-purchased/:address
  
  // 获取我拥有的专利
  GET /api/patents/my-owned/:address
  
  // 更新专利状态
  PUT /api/patents/:id/status
  Body: { status: 'FROZEN' | 'ACTIVE', address: string }
  
  // 撤销专利
  DELETE /api/patents/:id
  Body: { address: string }
  
  // 重新上架专利
  PUT /api/patents/:id/relist
  Body: { price: number, address: string }
  
  // 获取交易合同
  GET /api/trades/:id/contract
  ```

### 2.4 专利交易模块 (Patent Trading Module)

#### 2.4.1 专利交易流程
- **路由**: `/patent/:id/trade`
- **功能描述**: 
  - 确认交易信息：专利详情、转让价格、卖方信息
  - 验证买方钱包余额是否充足
  - 发起交易申请，调用智能合约
  - 交易申请提交后专利状态变为"交易中"
  - 等待审核方审核交易
  - 交易成功后自动转移专利权和资金
- **组件**: 
  - `PatentTrade.vue` - 交易发起页面
  - `TradeConfirmation.vue` - 交易确认弹窗
- **API接口**:
  ```javascript
  // 发起专利交易
  POST /api/trades
  Body: {
    patentId: number,
    buyerAddress: string,
    sellerAddress: string,
    price: number
  }
  
  // 获取交易状态
  GET /api/trades/:id/status
  ```

#### 2.4.2 交易合同
- **功能描述**: 
  - 交易审核通过后自动生成交易合同
  - 合同包含：交易双方信息、专利信息、交易价格、交易时间、审核员信息
  - 支持合同下载和弹窗查看
  - 合同作为交易凭证永久保存
- **组件**: `TradeContract.vue`
- **API接口**:
  ```javascript
  // 生成交易合同
  POST /api/trades/:id/generate-contract
  
  // 获取交易合同
  GET /api/trades/:id/contract
  
  // 下载交易合同
  GET /api/trades/:id/contract/download
  ```

### 2.5 专利维权模块 (Patent Rights Protection Module)

#### 2.5.1 维权申请
- **路由**: `/patent/:id/rights-protection`
- **功能描述**: 
  - 显示目标专利的区块链地址和基本信息
  - 填写维权描述文字，说明维权理由
  - 上传专利权证明文档到IPFS
  - 提交维权申请后等待审核
  - 维权成功后专利权将转移给维权申请人
- **组件**: 
  - `RightsProtection.vue` - 维权申请主页面
  - `RightsEvidenceUpload.vue` - 证明文档上传组件
- **API接口**:
  ```javascript
  // 提交维权申请
  POST /api/rights-protection
  Body: {
    patentId: number,
    claimantAddress: string,
    description: string,
    evidenceHash: string  // 证明文档IPFS哈希
  }
  
  // 获取维权申请状态
  GET /api/rights-protection/:id/status
  ```

### 2.6 审核管理模块 (Audit Management Module)

#### 2.6.1 待审核列表
- **路由**: `/audit/pending`
- **功能描述**: 
  - 显示所有待审核事务，按类型分类：专利上传、专利交易、专利维权
  - 专利上传审核：显示专利名称、上传用户信息（可点击查看详细）、上传时间、专利详细信息、文档查看下载
  - 专利交易审核：显示专利名称、交易双方信息、交易价格、专利详细信息
  - 专利维权审核：显示专利信息、维权申请人信息、维权描述、证明文档
  - 审核操作：通过/拒绝，拒绝需填写拒绝原因
- **组件**: 
  - `AuditPending.vue` - 待审核列表主页面
  - `PatentAuditCard.vue` - 专利上传审核卡片
  - `TradeAuditCard.vue` - 交易审核卡片
  - `RightsAuditCard.vue` - 维权审核卡片
  - `AuditForm.vue` - 审核操作表单
- **API接口**:
  ```javascript
  // 获取待审核列表
  GET /api/audits/pending
  Query: { 
    type?: 'PATENT_UPLOAD' | 'PATENT_TRADE' | 'RIGHTS_PROTECTION',
    page?: number,
    limit?: number
  }
  
  // 审核通过
  POST /api/audits/:id/approve
  Body: { 
    auditorAddress: string,
    reason?: string
  }
  
  // 审核拒绝
  POST /api/audits/:id/reject
  Body: { 
    auditorAddress: string,
    reason: string
  }
  ```

#### 2.6.2 审核记录
- **路由**: `/audit/history`
- **功能描述**: 
  - 显示历史审核记录，按审核类型分类
  - 记录包含：审核对象信息、审核结果、审核原因、审核时间、审核员信息
  - 支持按时间、类型、结果筛选
  - 提供详细的审核日志查看
- **组件**: 
  - `AuditHistory.vue` - 审核记录主页面
  - `AuditRecord.vue` - 审核记录详情组件
- **API接口**:
  ```javascript
  // 获取审核历史记录
  GET /api/audits/history
  Query: {
    auditorAddress?: string,
    type?: string,
    result?: 'APPROVED' | 'REJECTED',
    startDate?: string,
    endDate?: string,
    page?: number,
    limit?: number
  }
  
  // 获取审核记录详情
  GET /api/audits/:id/detail
  ```

### 2.7 通知系统模块 (Notification System Module)

#### 2.7.1 通知中心
- **路由**: `/notifications`
- **功能描述**: 
  - 实时显示用户相关的所有通知
  - 通知类型：专利审核通过/不通过、专利被交易、专利交易结果、专利被维权、维权结果
  - 通知内容包含：专利名称、事件类型、时间、相关链接
  - 支持标记已读/未读状态
  - 支持通知删除和批量操作
- **组件**: 
  - `NotificationCenter.vue` - 通知中心主页面
  - `NotificationItem.vue` - 单条通知组件
  - `NotificationFilter.vue` - 通知筛选组件
- **API接口**:
  ```javascript
  // 获取用户通知列表
  GET /api/notifications/:address
  Query: {
    type?: string,
    isRead?: boolean,
    page?: number,
    limit?: number
  }
  
  // 标记通知已读
  PUT /api/notifications/:id/read
  
  // 批量标记已读
  PUT /api/notifications/batch-read
  Body: { 
    userAddress: string,
    notificationIds: number[]
  }
  
  // 删除通知
  DELETE /api/notifications/:id
  ```

### 2.8 统计面板模块 (Statistics Dashboard Module)

#### 2.8.1 用户统计面板
- **路由**: `/statistics/user`
- **功能描述**: 
  - 显示用户个人统计数据
  - 上传专利数量：用户已上传的专利总数
  - 售出专利数量：已成功交易出售的专利数量
  - 购买专利数量：用户购买的专利总数
  - 钱包余额：当前钱包可用余额
  - 数据图表展示：饼图、柱状图等可视化展示
  - 统计数据实时更新
- **组件**: 
  - `UserStatistics.vue` - 用户统计主页面
  - `StatisticsCard.vue` - 统计卡片组件
  - `StatisticsChart.vue` - 图表组件
- **API接口**:
  ```javascript
  // 获取用户统计数据
  GET /api/statistics/user/:address
  Response: {
    uploadedPatents: number,
    soldPatents: number,
    purchasedPatents: number,
    walletBalance: number,
    monthlyData: object,  // 月度数据用于图表
    categoryData: object  // 专利类别分布数据
  }
  ```

#### 2.8.2 管理员统计面板
- **路由**: `/statistics/admin`
- **功能描述**: 
  - 显示系统整体统计数据
  - 总审核统计：已审核的事务总数（按类型分类）
  - 今日审核数：当日完成的审核数量
  - 系统用户总数、专利总数、交易总数
  - 审核员工作量统计
  - 系统运行状态监控
- **组件**: 
  - `AdminStatistics.vue` - 管理员统计主页面
  - `SystemOverview.vue` - 系统概览组件
  - `AuditorWorkload.vue` - 审核员工作量组件
- **API接口**:
  ```javascript
  // 获取管理员统计数据
  GET /api/statistics/admin
  Response: {
    totalAudits: number,
    todayAudits: number,
    totalUsers: number,
    totalPatents: number,
    totalTrades: number,
    auditorStats: array,  // 审核员统计数据
    systemHealth: object  // 系统健康状态
  }
  ```

### 2.9 管理员模块 (Administrator Module)

#### 2.9.1 用户管理
- **路由**: `/admin/users`
- **功能描述**: 
  - 显示所有注册用户列表
  - 用户信息查看：区块链地址、姓名、手机号、身份证号、注册时间
  - 用户权限管理：设置用户为普通用户或审核员
  - 用户状态管理：激活/禁用用户账户
  - 用户活动统计：上传专利数、交易次数等
  - 支持按姓名、手机号、地址搜索用户
- **组件**: 
  - `UserManagement.vue` - 用户管理主页面
  - `UserTable.vue` - 用户列表表格
  - `UserDetailModal.vue` - 用户详情弹窗
  - `PermissionEditor.vue` - 权限编辑组件
- **API接口**:
  ```javascript
  // 获取所有用户列表
  GET /api/admin/users
  Query: {
    search?: string,
    role?: 'USER' | 'AUDITOR',
    status?: 'ACTIVE' | 'INACTIVE',
    page?: number,
    limit?: number
  }
  
  // 更新用户权限
  PUT /api/admin/users/:address/role
  Body: { 
    role: 'USER' | 'AUDITOR',
    adminAddress: string
  }
  
  // 更新用户状态
  PUT /api/admin/users/:address/status
  Body: { 
    status: 'ACTIVE' | 'INACTIVE',
    adminAddress: string
  }
  
  // 获取用户详细信息
  GET /api/admin/users/:address/detail
  ```

#### 2.9.2 系统日志管理
- **路由**: `/admin/logs`
- **功能描述**: 
  - 显示系统操作日志，包括用户操作和系统事件
  - 日志类型：用户登录、专利上传、交易操作、审核操作、系统错误等
  - 日志详情：操作时间、操作用户、操作类型、操作结果、IP地址
  - 支持按时间范围、操作类型、用户地址筛选日志
  - 日志导出功能，支持CSV、Excel格式
  - 实时日志监控和告警
- **组件**: 
  - `LogViewer.vue` - 日志查看主页面
  - `LogFilter.vue` - 日志筛选组件
  - `LogTable.vue` - 日志表格组件
  - `LogDetailModal.vue` - 日志详情弹窗
- **API接口**:
  ```javascript
  // 获取系统日志
  GET /api/admin/logs
  Query: {
    startDate?: string,
    endDate?: string,
    logType?: string,
    userAddress?: string,
    level?: 'INFO' | 'WARN' | 'ERROR',
    page?: number,
    limit?: number
  }
  
  // 导出日志
  GET /api/admin/logs/export
  Query: {
    format: 'csv' | 'excel',
    startDate?: string,
    endDate?: string,
    logType?: string
  }
  
  // 获取日志统计
  GET /api/admin/logs/statistics
  ```

#### 2.9.3 系统配置管理
- **路由**: `/admin/settings`
- **功能描述**: 
  - 系统参数配置：交易手续费率、审核超时时间等
  - IPFS节点配置管理
  - 区块链网络配置
  - 系统维护模式开关
  - 备份和恢复功能
- **组件**: 
  - `SystemSettings.vue` - 系统设置主页面
  - `ConfigEditor.vue` - 配置编辑组件
- **API接口**:
  ```javascript
  // 获取系统配置
  GET /api/admin/settings
  
  // 更新系统配置
  PUT /api/admin/settings
  Body: {
    config: object,
    adminAddress: string
  }
  
  // 系统备份
  POST /api/admin/backup
  
  // 系统恢复
  POST /api/admin/restore
  Body: { backupFile: File }
  ```

## 3. 后端模块设计

### 3.1 用户认证服务 (Auth Service)

#### 3.1.1 API接口
```
POST /api/auth/login - 用户登录
POST /api/auth/logout - 用户退出
POST /api/auth/reset-password - 重置密码
GET /api/auth/profile - 获取用户信息
PUT /api/auth/profile - 更新用户信息
```

#### 3.1.2 功能实现
- JWT令牌生成和验证
- 密码加密存储
- 用户权限验证
- 区块链地址绑定

### 3.2 专利管理服务 (Patent Service)

#### 3.2.1 API接口
```
POST /api/patents - 上传专利
GET /api/patents - 搜索专利
GET /api/patents/:id - 获取专利详情
PUT /api/patents/:id - 更新专利信息
DELETE /api/patents/:id - 删除专利
GET /api/patents/:id/events - 获取专利事务
PUT /api/patents/:id/status - 更新专利状态
```

#### 3.2.2 功能实现
- 专利数据上链存储
- IPFS文件上传管理
- 专利搜索索引
- 专利状态管理
- 专利事务记录

### 3.3 交易管理服务 (Trade Service)

#### 3.3.1 API接口
```
POST /api/trades - 发起交易
GET /api/trades - 获取交易列表
GET /api/trades/:id - 获取交易详情
PUT /api/trades/:id/approve - 审核交易
POST /api/trades/:id/contract - 生成交易合同
```

#### 3.3.2 功能实现
- 智能合约交易执行
- 交易状态管理
- 交易合同生成
- 钱包余额管理

### 3.4 维权管理服务 (Rights Service)

#### 3.4.1 API接口
```
POST /api/rights-protection - 提交维权申请
GET /api/rights-protection - 获取维权列表
PUT /api/rights-protection/:id/approve - 审核维权
```

#### 3.4.2 功能实现
- 维权申请处理
- 维权审核流程
- 专利权转移

### 3.5 审核管理服务 (Audit Service)

#### 3.5.1 API接口
```
GET /api/audits/pending - 获取待审核列表
POST /api/audits/:id/approve - 审核通过
POST /api/audits/:id/reject - 审核拒绝
GET /api/audits/history - 获取审核历史
```

#### 3.5.2 功能实现
- 审核任务分发
- 审核结果记录
- 审核通知发送

### 3.6 通知服务 (Notification Service)

#### 3.6.1 API接口
```
GET /api/notifications - 获取通知列表
PUT /api/notifications/:id/read - 标记通知已读
POST /api/notifications - 发送通知
```

#### 3.6.2 功能实现
- 实时通知推送
- 通知模板管理
- 通知状态跟踪

### 3.7 钱包服务 (Wallet Service)

#### 3.7.1 API接口
```
GET /api/wallet/balance - 获取余额
POST /api/wallet/recharge - 充值
POST /api/wallet/withdraw - 提现
GET /api/wallet/transactions - 获取流水记录
```

#### 3.7.2 功能实现
- 虚拟钱包管理
- 交易流水记录
- 余额计算

### 3.8 统计服务 (Statistics Service)

#### 3.8.1 API接口
```
GET /api/statistics/user - 获取用户统计
GET /api/statistics/admin - 获取管理员统计
GET /api/statistics/system - 获取系统统计
```

#### 3.8.2 功能实现
- 数据统计计算
- 报表生成
- 图表数据提供

### 3.9 IPFS服务 (IPFS Service)

#### 3.9.1 API接口
```
POST /api/ipfs/upload - 上传文件到IPFS
GET /api/ipfs/:hash - 从IPFS获取文件
```

#### 3.9.2 功能实现
- IPFS节点连接
- 文件上传下载
- 哈希值管理