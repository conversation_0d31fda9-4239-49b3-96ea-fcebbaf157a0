功能
文档（word,pdf,png）都用IPFS存储
总的来说，就是基于区块链的专利交易系统，专利可以上传，上传专利专利就可以上链，并且可用被检索到，但是未通过审核时专利都是审核中的状态，只能被检索查看而不能交易，同样的，在进行交易时也可以检索到专利并且查看专利详细信息，但是专利状态为（交易中），维权也是同理。购买到的专利可以通过管理专利开放出售二次销售。

登录：电话号码，密码
修改密码
个人信息修改

用户管理（超级管理员）：审核功能（与专利检查方一致），专利检索，日志查看，账号管理，这是管理员的权限

普通用户
个人中心
姓名，手机号，身份证号，区块链地址

专利上传：专利名称，专利号，专利类别，转让价格，专利摘要，专利申请日期，专利权结束日期，专利权人姓名，专利权人身份证号。是否为代理出售，是就上传专利代理委托证明文档，不是就上传专利权证明文档
上传文件：专利文档或专利代理证明文档

专利搜索
专利名（模糊搜索/精准搜索），专利号，专利类别

专利详细信息：专利区块链信息专利上传人（点击查看姓名，手机号），专利名称，专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，是否代理 （可查看专利代理委托证明文档），不是代理就可查看专利权证明文档，专利权人姓名，专利交易按钮，专利事务
可查看和下载文件：专利文档，专利代理证明文档

专利事务（每一个专利都有专利事务可查看）：相当于专利溯源，显示每一个操作的参与人及其区块链地址，如上传事件上传人的区块链地址和审核员的区块链地址，交易就显示二人链码，审核员链码，事件类型为交易，还要有时间（交易成功使时间），专利维权事件（维权人，维权人链码，时间，审核员，审核员链码），

专利维权：专利区块链地址（点击查看专利详细信息），专利号，专利名称，专利权证明文档，维权描述文字。
提交专利维权审核成功后就会让那个专利权变更为专利维权的人。

我购买的专利
专利名称，专利号，时间，专利详细信息（专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，专利交易信息（交易双方姓名（售出专利的以上传专利的为准），身份证号，链码，审核员姓名，链码，交易成功时间），是否为代理出售，专利文档或专利代理证明文档，交易报表（智能合约生成）

我发布的专利
专利名称，专利号，发布时间，专利详细信息（专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，专利权人姓名，专利权人身份证号。是否为代理出售，专利文档或专利代理证明文档，交易报表（智能合约生成）），状态（审核中 正常 ）  操作（撤销专利，专利冻结（不可交易），专利恢复（可交易）），要是专利已经交易能够显示交易详情

我的专利
显示我当前所有拥有专利权的专利（没有售出的专利+我买到的专利）
专利名称，专利号，发布时间，专利详细信息（专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，如果交易过能看到交易报表（智能合约生成） 

通知功能，审核通过，专利被交易，专利交易结果，专利被维权，专利维权结果都能有通知
显示内容：专利名称 时间 事件（上传审核通过/不通过，维权成功/失败，发起交易/交易失败）
钱包功能（模拟即可，充值、提现、交易后钱包的余额有所加减）
充值/提现
流水记录 +/- 专利名称，时间


基础统计：上传专利几项，售出专利几项，购买专利几项，余额

专利审核方
待审核
审核事务分类为：专利上传、专利维权，专利交易

专利上传审核
专利名称，上传用户区块链地址（点击查看详细信息，如姓名，手机号，身份证号），时间，详细信息（专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，专利权人名字，专利权人身份证号。是否为代理出售，专利文档或专利代理证明文档的查看与下载
），状态（通过 拒绝），拒绝会返回一个拒绝原因输入框，这个信息能够反馈给用户。

专利维权审核:
专利区块链地址（点击显示对应区块链信息）描述文字（点击显示）专利证明文档（点击显示或下载）状态（通过 拒绝），拒绝也要有输入框输入拒绝原因

专利交易：名称，专利上传人区块链地址（点击查看详细信息，如姓名，手机号，身份证号），专利购买人区块链地址（点击查看详细信息，如姓名，手机号，身份证号），专利价格，专利详细信息（点击显示：专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，专利上传人姓名，专利权人身份证号。专利购买人名字和身份证号是否为代理出售，专利文档或专利代理证明文档的查看与下载） 操作（通过 不通过）不通过要有输入框输入原因。

专利审核记录
审核记录分类为：专利上传、专利维权，专利交易

专利上传审核
专利名称，上传用户区块链地址（点击查看详细信息，如姓名，手机号，身份证号），时间，详细信息（专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，专利权人名字，专利权人身份证号。是否为代理出售，专利文档或专利代理证明文档的查看与下载
），操作记录（通过 拒绝），拒绝原因。

专利维权记录:
专利区块链地址（点击显示对应区块链信息）描述文字（点击显示）专利证明文档（点击显示或下载） 操作记录（通过 拒绝），拒绝原因。
专利交易：名称，专利上传人区块链地址（点击查看详细信息，如姓名，手机号，身份证号），专利购买人区块链地址（点击查看详细信息，如姓名，手机号，身份证号），专利价格，专利详细信息（点击显示：专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，专利权人姓名，专利权人身份证号。是否为代理出售，专利文档或专利代理证明文档的查看与下载）

专利交易记录
名称，专利上传人区块链地址（点击查看详细信息，如姓名，手机号，身份证号），专利购买人区块链地址（点击查看详细信息，如姓名，手机号，身份证号），专利价格，专利详细信息（点击显示：专利号，转让价格，专利摘要，专利申请日期，专利权结束日期，专利权人姓名，专利权人身份证号。是否为代理出售，专利文档或专利代理证明文档的查看与下载） 操作（通过 不通过）不通过要有不通过原因的展示  ，  能不能把这些信息生成一个文件，当作是他们的交易合同，如果不能，那就做一个弹窗，当作是他们的合同。

基础统计：总审核统计，今日审核数

个人中心
姓名，手机号，身份证号，区块链地址 