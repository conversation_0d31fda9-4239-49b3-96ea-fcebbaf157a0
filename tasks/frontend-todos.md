# Frontend Todo List - Blockchain Patent Exchange System

## 1. MetaMask Integration Module

### 1.1 Wallet Connection
- [x] Create MetaMaskConnector.vue component
- [x] Implement wallet installation detection
- [x] Implement wallet connection functionality
- [x] Add account switching monitoring
- [x] Add network change detection
- [x] Implement Ganache private chain validation
- [x] Create NetworkValidator.vue component
- [x] Add connection status display
- [x] Implement network switching guidance

### 1.2 Authentication Integration
- [x] Integrate with auth check API endpoint
- [x] Implement user registration check by address
- [x] Add user info retrieval functionality

## 2. User Center Module

### 2.1 Personal Information Management
- [x] Create UserProfile.vue component
- [x] Implement profile route `/profile`
- [x] Display blockchain address (read-only)
- [x] Add personal info form (name, phone, ID card)
- [x] Implement first-time user info completion requirement
- [x] Add blockchain transaction confirmation for info updates
- [x] Integrate with profile API endpoints

### 2.2 Wallet Functionality
- [x] Create Wallet.vue main component
- [x] Implement wallet route `/wallet`
- [x] Display wallet balance from smart contract
- [x] Create RechargeModal.vue component
- [x] Create WithdrawModal.vue component
- [x] Create TransactionHistory.vue component
- [x] Implement simulated recharge functionality
- [x] Implement simulated withdrawal functionality
- [x] Display transaction history (income/expense, patent name, time)
- [x] Integrate with wallet API endpoints

## 3. Patent Management Module

### 3.1 Patent Upload
- [x] Create PatentUpload.vue component
- [x] Implement upload route `/patent/upload`
- [x] Add patent basic info form (name, number, category, price, summary, dates)
- [x] Add patent owner info form (name, ID card)
- [x] Add agency sale option (radio button)
- [x] Implement conditional document upload based on agency status
- [x] Add patent document upload functionality
- [x] Support multiple file formats (Word, PDF, PNG)
- [x] Integrate with IPFS upload API
- [x] Set patent status to "under review" after upload
- [x] Integrate with patent creation API

### 3.2 Patent Search
- [x] Create PatentSearch.vue component
- [x] Create PatentList.vue component
- [x] Create PatentCard.vue component
- [x] Implement search route `/patent/search`
- [x] Add fuzzy and exact search by patent name
- [x] Add exact search by patent number
- [x] Add category filtering
- [x] Implement pagination for search results
- [x] Display search results with patent info
- [x] Add click-to-view-details functionality
- [x] Integrate with search API endpoints

### 3.3 Patent Detail Page
- [x] Create PatentDetail.vue component
- [x] Create PatentDocuments.vue component
- [x] Create PatentEvents.vue component
- [x] Create TradeButton.vue component
- [x] Implement detail route `/patent/:id`
- [x] Display complete patent information
- [x] Show uploader information with click-to-view functionality
- [x] Display agency sale status
- [x] Implement document viewing and download
- [x] Show trade button for eligible patents
- [x] Display patent event traceability
- [x] Integrate with patent detail API

### 3.4 My Patents Management
- [x] Create MyPatents.vue main component
- [x] Create PatentStatusControl.vue component (as PublishedPatentsList.vue)
- [ ] Create TradeContractModal.vue component
- [x] Implement published patents route `/my-patents/published`
- [x] Implement purchased patents route `/my-patents/purchased`
- [x] Implement owned patents route `/my-patents/owned`
- [x] Display published patents with status and management options
- [x] Add patent revocation functionality
- [x] Add patent freeze/unfreeze functionality
- [ ] Display purchased patents with transaction details
- [ ] Show trade contract download for completed trades
- [ ] Display currently owned patents with re-listing capability
- [x] Integrate with my patents API endpoints

## 4. Patent Trading Module

### 4.1 Patent Trading Process
- [ ] Create PatentTrade.vue component
- [ ] Create TradeConfirmation.vue component
- [ ] Implement trade route `/patent/:id/trade`
- [ ] Display trade confirmation info (patent details, price, seller info)
- [ ] Implement wallet balance verification
- [ ] Add trade initiation with smart contract call
- [ ] Set patent status to "trading" after application
- [ ] Handle trade approval workflow
- [ ] Implement automatic ownership and fund transfer
- [ ] Integrate with trading API endpoints

### 4.2 Trade Contract
- [ ] Create TradeContract.vue component
- [ ] Implement automatic contract generation after approval
- [ ] Include complete trade information in contract
- [ ] Add contract download functionality
- [ ] Add contract modal viewing
- [ ] Store contract as permanent trade evidence
- [ ] Integrate with contract API endpoints

## 5. Patent Rights Protection Module

### 5.1 Rights Protection Application
- [ ] Create RightsProtection.vue component
- [ ] Create RightsEvidenceUpload.vue component
- [ ] Implement rights protection route `/patent/:id/rights-protection`
- [ ] Display target patent blockchain address and info
- [ ] Add rights protection description form
- [ ] Implement evidence document upload to IPFS
- [ ] Submit rights protection application
- [ ] Handle approval workflow
- [ ] Implement ownership transfer upon successful protection
- [ ] Integrate with rights protection API

## 6. Audit Management Module

### 6.1 Pending Audit List
- [ ] Create AuditPending.vue main component
- [ ] Create PatentAuditCard.vue component
- [ ] Create TradeAuditCard.vue component
- [ ] Create RightsAuditCard.vue component
- [ ] Create AuditForm.vue component
- [ ] Implement audit route `/audit/pending`
- [ ] Display categorized pending audits (upload, trade, rights protection)
- [ ] Show patent upload audit details with document access
- [ ] Show trade audit details with party information
- [ ] Show rights protection audit with evidence documents
- [ ] Implement approve/reject actions with reason input
- [ ] Integrate with audit API endpoints

### 6.2 Audit History
- [ ] Create AuditHistory.vue component
- [ ] Create AuditRecord.vue component
- [ ] Implement audit history route `/audit/history`
- [ ] Display historical audit records by category
- [ ] Show audit details (object, result, reason, time, auditor)
- [ ] Add filtering by time, type, and result
- [ ] Provide detailed audit log viewing
- [ ] Integrate with audit history API

## 7. Notification System Module

### 7.1 Notification Center
- [ ] Create NotificationCenter.vue component
- [ ] Create NotificationItem.vue component
- [ ] Create NotificationFilter.vue component
- [ ] Implement notifications route `/notifications`
- [ ] Display real-time user notifications
- [ ] Show notification types (audit results, trades, rights protection)
- [ ] Include patent name, event type, time, and related links
- [ ] Implement read/unread status management
- [ ] Add notification deletion and batch operations
- [ ] Integrate with notifications API


## 8. Administrator Module

### 8.1 User Management
- [ ] Create UserManagement.vue component
- [ ] Create UserTable.vue component
- [ ] Create UserDetailModal.vue component
- [ ] Create PermissionEditor.vue component
- [ ] Implement user management route `/admin/users`
- [ ] Display all registered users list
- [ ] Show user info (address, name, phone, ID, registration time)
- [ ] Implement user permission management (user/auditor roles)
- [ ] Add user status management (active/inactive)
- [ ] Display user activity statistics
- [ ] Add user search functionality (name, phone, address)
- [ ] Integrate with user management API

### 8.2 System Configuration Management
- [ ] Create SystemSettings.vue component
- [ ] Create ConfigEditor.vue component
- [ ] Implement settings route `/admin/settings`
- [ ] Add system parameter configuration (transaction fees, timeout)
- [ ] Implement IPFS node configuration management
- [ ] Add blockchain network configuration
- [ ] Implement system maintenance mode toggle
- [ ] Add backup and restore functionality
- [ ] Integrate with settings API
