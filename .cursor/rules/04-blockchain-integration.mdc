---
description:
globs:
alwaysApply: false
---
# Blockchain Integration Guidelines

## Smart Contract Architecture
Based on [backend-todos.md](mdc:tasks/backend-todos.md), implement core smart contracts for the patent exchange system:

### Core Smart Contracts
```
contracts/
├── UserManagement.sol          # User registration and profile management
├── PatentManagement.sol        # Patent creation and ownership tracking  
├── TradingManagement.sol       # Patent trading with escrow functionality
├── WalletManagement.sol        # Wallet balance and transaction management
├── RightsProtection.sol        # Patent rights protection claims
├── AuditManagement.sol         # Audit workflow management
├── NotificationManagement.sol  # Notification system
└── SystemConfiguration.sol     # System settings and configuration
```

### Smart Contract Functions

#### User Management Contract
- `registerUser(address _user, string _name, string _phone, string _idCard)` - Register new user
- `updateProfile(address _user, string _name, string _phone, string _idCard)` - Update user profile
- `getUserInfo(address _user)` - Get user information
- `setUserRole(address _user, uint8 _role)` - Set user role (user/auditor)
- `isUserRegistered(address _user)` - Check if user is registered

#### Patent Management Contract
- `createPatent(string _name, string _number, string _category, uint256 _price, string _ipfsHash)` - Create patent
- `transferOwnership(uint256 _patentId, address _newOwner)` - Transfer patent ownership
- `updatePatentStatus(uint256 _patentId, uint8 _status)` - Update patent status
- `getPatentInfo(uint256 _patentId)` - Get patent information
- `getPatentsByOwner(address _owner)` - Get patents owned by address
- `freezePatent(uint256 _patentId)` - Freeze patent (admin only)

#### Trading Management Contract
- `initiateTrade(uint256 _patentId, address _buyer)` - Start trade process
- `approveTrade(uint256 _tradeId)` - Approve trade (admin)
- `executeTradeTransfer(uint256 _tradeId)` - Execute ownership and fund transfer
- `getTradeInfo(uint256 _tradeId)` - Get trade details
- `cancelTrade(uint256 _tradeId)` - Cancel trade

#### Wallet Management Contract
- `deposit(address _user, uint256 _amount)` - Add funds to wallet
- `withdraw(address _user, uint256 _amount)` - Withdraw funds
- `getBalance(address _user)` - Get wallet balance
- `transfer(address _from, address _to, uint256 _amount)` - Transfer funds
- `recordTransaction(address _user, uint256 _amount, string _description)` - Record transaction

## Ganache Development Setup

### Network Configuration
```javascript
// truffle-config.js
module.exports = {
  networks: {
    development: {
      host: "127.0.0.1",
      port: 7545,
      network_id: "*",
      gas: 6721975,
      gasPrice: ***********
    }
  },
  compilers: {
    solc: {
      version: "^0.8.0"
    }
  }
};
```

### Deployment Strategy
1. **Start Ganache**: Launch Ganache with predetermined accounts
2. **Deploy Contracts**: Deploy all smart contracts in dependency order
3. **Initialize Data**: Set up initial system configuration
4. **Verify Deployment**: Test contract interactions
5. **Configure Backend**: Update contract addresses in backend

### Contract Interaction Patterns
```javascript
// Example contract interaction
const Web3 = require('web3');
const web3 = new Web3('http://localhost:7545');

// Contract interaction example
async function createPatent(patentData) {
  const contract = new web3.eth.Contract(PatentManagementABI, contractAddress);
  const result = await contract.methods.createPatent(
    patentData.name,
    patentData.number,
    patentData.category,
    patentData.price,
    patentData.ipfsHash
  ).send({ from: userAddress, gas: 300000 });
  
  return result;
}
```

## IPFS Integration Patterns

### IPFS Setup and Configuration
- **Node Setup**: Configure IPFS node for document storage
- **File Upload**: Handle multiple file formats (Word, PDF, PNG)
- **Hash Management**: Store IPFS hashes on blockchain
- **Access Control**: Implement document access permissions
- **Verification**: Validate file integrity using hashes

### IPFS Service Structure
```javascript
// IPFS service example
class IPFSService {
  constructor() {
    this.ipfs = require('ipfs-http-client')({ 
      host: 'localhost', 
      port: 5001, 
      protocol: 'http' 
    });
  }

  async uploadFile(file) {
    const result = await this.ipfs.add(file);
    return result.path; // IPFS hash
  }

  async getFile(hash) {
    const chunks = [];
    for await (const chunk of this.ipfs.cat(hash)) {
      chunks.push(chunk);
    }
    return Buffer.concat(chunks);
  }
}
```

## Blockchain Event Handling

### Event Listening Patterns
- **Real-time Updates**: Listen to contract events for UI updates
- **Notification Triggers**: Generate notifications from blockchain events
- **Data Synchronization**: Keep frontend state in sync with blockchain
- **Error Handling**: Handle blockchain transaction failures gracefully

### Event Listener Implementation
```javascript
// Event listener example
const contract = new web3.eth.Contract(PatentManagementABI, contractAddress);

contract.events.PatentCreated({}, (error, event) => {
  if (error) {
    console.error('Event listener error:', error);
    return;
  }
  
  // Handle patent creation event
  const { patentId, owner, name } = event.returnValues;
  notificationService.createNotification(owner, 'Patent created successfully', 'patent');
});
```

## Development Guidelines

### Transaction Management
- **Gas Optimization**: Optimize smart contract functions for gas efficiency
- **Transaction Confirmation**: Wait for transaction confirmation before updating UI
- **Error Handling**: Implement comprehensive error handling for failed transactions
- **Retry Logic**: Implement retry mechanisms for failed blockchain operations

### Security Considerations
- **Input Validation**: Validate all inputs before blockchain submission
- **Access Control**: Implement proper role-based access control
- **Reentrancy Protection**: Use reentrancy guards in smart contracts
- **Integer Overflow**: Use SafeMath or Solidity ^0.8.0 for overflow protection

### Testing Strategy
- **Unit Tests**: Test individual smart contract functions
- **Integration Tests**: Test contract interactions and workflows
- **Frontend Tests**: Test MetaMask integration and user flows
- **End-to-End Tests**: Test complete patent lifecycle workflows
