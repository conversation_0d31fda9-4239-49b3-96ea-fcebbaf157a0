---
description:
globs:
alwaysApply: false
---
# State Management and Data Flow

## Vue.js Store Architecture
Based on the frontend modules in [frontend-todos.md](mdc:tasks/frontend-todos.md), organize Vuex store by feature:

### Store Structure
```
src/store/
├── index.js                    # Root store configuration
├── modules/
│   ├── auth.js                # MetaMask authentication state
│   ├── user.js                # User profile and wallet state
│   ├── patents.js             # Patent management state
│   ├── trades.js              # Trading workflow state
│   ├── rightsProtection.js    # Rights protection state
│   ├── audits.js              # Audit management state
│   ├── notifications.js       # Notification system state
│   └── admin.js               # Admin functionality state
```

## Core State Modules

### Authentication Store (auth.js)
```javascript
// MetaMask authentication and network state
const state = {
  isConnected: false,
  account: null,
  networkId: null,
  isValidNetwork: false,
  isRegistered: false,
  userRole: 'user', // 'user' | 'auditor' | 'admin'
  connectionError: null
};

const mutations = {
  SET_CONNECTION_STATUS: (state, status) => { state.isConnected = status; },
  SET_ACCOUNT: (state, account) => { state.account = account; },
  SET_NETWORK: (state, networkId) => { state.networkId = networkId; },
  SET_NETWORK_VALIDITY: (state, isValid) => { state.isValidNetwork = isValid; },
  SET_REGISTRATION_STATUS: (state, isRegistered) => { state.isRegistered = isRegistered; },
  SET_USER_ROLE: (state, role) => { state.userRole = role; },
  SET_CONNECTION_ERROR: (state, error) => { state.connectionError = error; }
};

const actions = {
  async connectWallet({ commit, dispatch }) {
    // MetaMask connection logic
  },
  async checkUserRegistration({ commit, state }) {
    // Check if user is registered on blockchain
  },
  async switchToGanache({ commit }) {
    // Guide user to switch to Ganache network
  }
};
```

### User Store (user.js)
```javascript
// User profile and wallet management state
const state = {
  profile: {
    address: null,
    name: '',
    phone: '',
    idCard: '',
    registrationTime: null,
    isProfileComplete: false
  },
  wallet: {
    balance: 0,
    transactions: [],
    isLoading: false
  }
};

const mutations = {
  SET_PROFILE: (state, profile) => { state.profile = { ...state.profile, ...profile }; },
  SET_WALLET_BALANCE: (state, balance) => { state.wallet.balance = balance; },
  SET_TRANSACTIONS: (state, transactions) => { state.wallet.transactions = transactions; },
  SET_WALLET_LOADING: (state, loading) => { state.wallet.isLoading = loading; }
};

const actions = {
  async fetchProfile({ commit, rootState }) {
    // Fetch user profile from blockchain
  },
  async updateProfile({ commit }, profileData) {
    // Update profile on blockchain
  },
  async fetchWalletBalance({ commit, rootState }) {
    // Get wallet balance from smart contract
  },
  async rechargeWallet({ commit, dispatch }, amount) {
    // Simulate wallet recharge
  }
};
```

### Patents Store (patents.js)
```javascript
// Patent management state
const state = {
  patents: [],
  categories: [],
  searchResults: [],
  currentPatent: null,
  myPublishedPatents: [],
  myPurchasedPatents: [],
  myOwnedPatents: [],
  uploadStatus: null,
  searchFilters: {
    query: '',
    category: '',
    exactSearch: false
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0
  }
};

const mutations = {
  SET_PATENTS: (state, patents) => { state.patents = patents; },
  SET_CATEGORIES: (state, categories) => { state.categories = categories; },
  SET_SEARCH_RESULTS: (state, results) => { state.searchResults = results; },
  SET_CURRENT_PATENT: (state, patent) => { state.currentPatent = patent; },
  SET_MY_PUBLISHED_PATENTS: (state, patents) => { state.myPublishedPatents = patents; },
  SET_MY_PURCHASED_PATENTS: (state, patents) => { state.myPurchasedPatents = patents; },
  SET_MY_OWNED_PATENTS: (state, patents) => { state.myOwnedPatents = patents; },
  SET_UPLOAD_STATUS: (state, status) => { state.uploadStatus = status; },
  SET_SEARCH_FILTERS: (state, filters) => { state.searchFilters = { ...state.searchFilters, ...filters }; },
  SET_PAGINATION: (state, pagination) => { state.pagination = { ...state.pagination, ...pagination }; }
};

const actions = {
  async uploadPatent({ commit }, patentData) {
    // Upload patent to IPFS and create on blockchain
  },
  async searchPatents({ commit, state }) {
    // Search patents with filters
  },
  async fetchPatentDetails({ commit }, patentId) {
    // Get patent details from blockchain
  },
  async fetchMyPatents({ commit, rootState }, type) {
    // Get user's patents by type (published/purchased/owned)
  }
};
```

### Trading Store (trades.js)
```javascript
// Patent trading workflow state
const state = {
  activeTrades: [],
  currentTrade: null,
  tradeContract: null,
  tradeStatus: null,
  buyerBalance: 0
};

const mutations = {
  SET_ACTIVE_TRADES: (state, trades) => { state.activeTrades = trades; },
  SET_CURRENT_TRADE: (state, trade) => { state.currentTrade = trade; },
  SET_TRADE_CONTRACT: (state, contract) => { state.tradeContract = contract; },
  SET_TRADE_STATUS: (state, status) => { state.tradeStatus = status; },
  SET_BUYER_BALANCE: (state, balance) => { state.buyerBalance = balance; }
};

const actions = {
  async initiateTrade({ commit, dispatch }, { patentId, buyerAddress }) {
    // Start patent trade process
  },
  async approveTrade({ commit }, tradeId) {
    // Approve trade (admin action)
  },
  async generateTradeContract({ commit }, tradeId) {
    // Generate trade contract after approval
  }
};
```

## Blockchain State Synchronization

### Real-time Event Handling
```javascript
// Event listener service for blockchain state updates
class BlockchainEventService {
  constructor(store) {
    this.store = store;
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Listen to patent creation events
    patentContract.events.PatentCreated({}, (error, event) => {
      if (!error) {
        this.store.dispatch('patents/handlePatentCreated', event.returnValues);
        this.store.dispatch('notifications/addNotification', {
          type: 'patent',
          message: 'Patent created successfully',
          data: event.returnValues
        });
      }
    });

    // Listen to trade events
    tradeContract.events.TradeInitiated({}, (error, event) => {
      if (!error) {
        this.store.dispatch('trades/handleTradeInitiated', event.returnValues);
      }
    });

    // Listen to wallet events
    walletContract.events.BalanceUpdated({}, (error, event) => {
      if (!error) {
        this.store.dispatch('user/updateWalletBalance', event.returnValues.newBalance);
      }
    });
  }
}
```

### State Persistence
```javascript
// Vuex persistence for offline capability
import VuexPersistence from 'vuex-persist';

const vuexLocal = new VuexPersistence({
  storage: window.localStorage,
  modules: ['auth', 'user'], // Persist critical state
  filter: (mutation) => {
    // Only persist non-sensitive state
    return !mutation.type.includes('SET_PASSWORD') && 
           !mutation.type.includes('SET_PRIVATE_KEY');
  }
});

export default new Vuex.Store({
  modules: {
    auth,
    user,
    patents,
    trades,
    rightsProtection,
    audits,
    notifications,
    admin
  },
  plugins: [vuexLocal.plugin]
});
```

## Data Flow Patterns

### Component-Store Interaction
```javascript
// Component example: PatentUpload.vue
export default {
  computed: {
    ...mapState('patents', ['uploadStatus', 'categories']),
    ...mapState('auth', ['account', 'isRegistered'])
  },
  methods: {
    ...mapActions('patents', ['uploadPatent', 'fetchCategories']),
    
    async handleUpload() {
      if (!this.isRegistered) {
        this.$router.push('/profile');
        return;
      }
      
      const result = await this.uploadPatent(this.patentData);
      if (result.success) {
        this.$router.push('/my-patents/published');
      }
    }
  },
  
  async created() {
    await this.fetchCategories();
  }
};
```

### API Service Integration
```javascript
// API service with store integration
class PatentService {
  static async uploadPatent(patentData) {
    try {
      // Upload to IPFS first
      const ipfsHash = await IPFSService.upload(patentData.document);
      
      // Create patent on blockchain
      const response = await api.post('/api/patents', {
        ...patentData,
        ipfsHash
      });
      
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}
```

## State Management Best Practices

### Loading States
```javascript
// Handle loading states consistently
const mutations = {
  SET_LOADING: (state, { action, status }) => {
    state.loading = { ...state.loading, [action]: status };
  }
};

// Usage in actions
const actions = {
  async fetchPatents({ commit }) {
    commit('SET_LOADING', { action: 'fetchPatents', status: true });
    try {
      const patents = await PatentService.fetchPatents();
      commit('SET_PATENTS', patents);
    } finally {
      commit('SET_LOADING', { action: 'fetchPatents', status: false });
    }
  }
};
```

### Error Handling
```javascript
// Centralized error handling
const state = {
  errors: {}
};

const mutations = {
  SET_ERROR: (state, { action, error }) => {
    state.errors = { ...state.errors, [action]: error };
  },
  CLEAR_ERROR: (state, action) => {
    const errors = { ...state.errors };
    delete errors[action];
    state.errors = errors;
  }
};
```

### Optimistic Updates
```javascript
// Optimistic updates for better UX
const actions = {
  async updatePatentStatus({ commit, state }, { patentId, newStatus }) {
    // Optimistically update UI
    const optimisticPatent = { ...state.currentPatent, status: newStatus };
    commit('SET_CURRENT_PATENT', optimisticPatent);
    
    try {
      await PatentService.updateStatus(patentId, newStatus);
    } catch (error) {
      // Revert on error
      commit('SET_CURRENT_PATENT', state.currentPatent);
      throw error;
    }
  }
};
```
