---
description:
globs:
alwaysApply: false
---
# Backend API Structure and Development Guidelines

## API Organization
Based on [backend-todos.md](mdc:tasks/backend-todos.md), organize Express.js APIs by feature modules:

### Core API Structure
```
src/
├── routes/
│   ├── auth/
│   │   ├── index.js
│   │   ├── check-user.js      # GET /api/auth/check-user/:address
│   │   ├── user-info.js       # GET /api/auth/user-info/:address
│   │   └── network.js         # GET /api/network/validate
│   ├── user/
│   │   ├── profile.js         # GET/PUT /api/user/profile/:address
│   │   └── wallet.js          # GET/POST /api/wallet/* endpoints
│   ├── patents/
│   │   ├── index.js           # GET/POST /api/patents (search/create)
│   │   ├── details.js         # GET /api/patents/:id
│   │   ├── categories.js      # GET /api/patents/categories
│   │   ├── events.js          # GET /api/patents/:id/events
│   │   ├── my-patents.js      # GET /api/patents/my-*/:address
│   │   └── management.js      # PUT /api/patents/:id/status|relist
│   ├── trades/
│   │   ├── index.js           # POST/GET /api/trades
│   │   ├── approval.js        # PUT /api/trades/:id/approve
│   │   ├── status.js          # GET /api/trades/:id/status
│   │   └── contracts.js       # GET/POST /api/trades/:id/contract*
│   ├── rights-protection/
│   │   ├── index.js           # POST /api/rights-protection
│   │   ├── status.js          # GET /api/rights-protection/:id/status
│   │   └── approval.js        # PUT /api/rights-protection/:id/approve
│   ├── audits/
│   │   ├── pending.js         # GET /api/audits/pending
│   │   ├── actions.js         # POST /api/audits/:id/approve|reject
│   │   ├── details.js         # GET /api/audits/:id/detail
│   │   └── history.js         # GET /api/audits/history
│   ├── notifications/
│   │   ├── index.js           # GET/POST/PUT/DELETE /api/notifications*
│   │   └── batch.js           # PUT /api/notifications/batch-read
│   ├── admin/
│   │   ├── users.js           # GET/PUT /api/admin/users*
│   │   ├── settings.js        # GET/PUT /api/admin/settings
│   │   └── statistics.js      # GET /api/statistics/*
│   └── ipfs/
│       ├── upload.js          # POST /api/ipfs/upload
│       └── download.js        # GET /api/ipfs/:hash/download
```

## API Endpoint Patterns

### Authentication Endpoints
- `GET /api/auth/check-user/:address` - Check if user is registered
- `GET /api/auth/user-info/:address` - Get user information
- `GET /api/network/validate` - Validate Ganache network connection

### User Management Endpoints
- `GET /api/user/profile/:address` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `GET /api/wallet/balance/:address` - Get wallet balance
- `POST /api/wallet/recharge` - Recharge wallet
- `POST /api/wallet/withdraw` - Withdraw from wallet
- `GET /api/wallet/transactions/:address` - Get transaction history

### Patent Management Endpoints
- `POST /api/patents` - Upload/create patent
- `GET /api/patents` - Search patents with filters
- `GET /api/patents/:id` - Get patent details
- `GET /api/patents/categories` - Get patent categories
- `GET /api/patents/:id/events` - Get patent event history
- `GET /api/patents/my-published/:address` - Get user's published patents
- `GET /api/patents/my-purchased/:address` - Get user's purchased patents
- `GET /api/patents/my-owned/:address` - Get user's owned patents
- `PUT /api/patents/:id/status` - Update patent status
- `PUT /api/patents/:id/relist` - Relist patent for sale

### Trading Endpoints
- `POST /api/trades` - Initiate trade
- `GET /api/trades/:id` - Get trade details
- `PUT /api/trades/:id/approve` - Approve trade
- `GET /api/trades/:id/status` - Get trade status
- `POST /api/trades/:id/generate-contract` - Generate trade contract
- `GET /api/trades/:id/contract` - Get trade contract
- `GET /api/trades/:id/contract/download` - Download contract

### Rights Protection Endpoints
- `POST /api/rights-protection` - Submit rights protection claim
- `GET /api/rights-protection/:id/status` - Get protection status
- `PUT /api/rights-protection/:id/approve` - Approve protection claim

### Audit Management Endpoints
- `GET /api/audits/pending` - Get pending audit tasks
- `POST /api/audits/:id/approve` - Approve audit item
- `POST /api/audits/:id/reject` - Reject audit item
- `GET /api/audits/:id/detail` - Get audit details
- `GET /api/audits/history` - Get audit history

### Notification Endpoints
- `GET /api/notifications/:address` - Get user notifications
- `PUT /api/notifications/:id/read` - Mark notification as read
- `PUT /api/notifications/batch-read` - Batch mark as read
- `POST /api/notifications` - Create notification
- `DELETE /api/notifications/:id` - Delete notification

### Admin Endpoints
- `GET /api/admin/users` - Get all users
- `PUT /api/admin/users/:address/role` - Update user role
- `PUT /api/admin/users/:address/status` - Update user status
- `GET /api/admin/users/:address/detail` - Get user details
- `GET /api/admin/settings` - Get system settings
- `PUT /api/admin/settings` - Update system settings
- `GET /api/statistics/admin` - Get admin statistics
- `GET /api/statistics/system` - Get system statistics

### IPFS Endpoints
- `POST /api/ipfs/upload` - Upload file to IPFS
- `GET /api/ipfs/:hash` - Get file from IPFS
- `GET /api/ipfs/:hash/download` - Download file from IPFS

## Middleware Architecture

### Core Middleware
- **Authentication Middleware**: Validate blockchain address and user registration
- **Authorization Middleware**: Check user roles and permissions
- **Validation Middleware**: Validate request data and parameters
- **Blockchain Middleware**: Handle smart contract interactions
- **Error Handling Middleware**: Process blockchain and application errors
- **Logging Middleware**: Log blockchain transactions and API requests

### Blockchain Integration Patterns
- **Smart Contract Interaction**: Use web3.js for contract calls
- **Transaction Management**: Handle gas fees and transaction confirmation
- **Event Listening**: Monitor blockchain events for real-time updates
- **Error Handling**: Proper blockchain error handling and user feedback
