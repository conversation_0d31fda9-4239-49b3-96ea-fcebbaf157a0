---
description:
globs:
alwaysApply: false
---
# Blockchain Patent Exchange System - Project Overview

## System Architecture
This is a blockchain-based patent exchange system built with:
- **Frontend**: Vue.js with MetaMask integration
- **Backend**: Node.js/Express.js with blockchain integration  
- **Blockchain**: Ganache local network with smart contracts
- **Storage**: IPFS for document storage
- **Database**: Hybrid approach with blockchain and traditional storage

## Core Modules Overview

### Frontend Modules ([frontend-todos.md](mdc:tasks/frontend-todos.md))
1. **MetaMask Integration** - Wallet connection and network validation
2. **User Center** - Profile management and wallet functionality
3. **Patent Management** - Upload, search, details, and personal patents
4. **Patent Trading** - Trading process and contract generation
5. **Rights Protection** - Patent rights claim system
6. **Audit Management** - Audit workflows for admins
7. **Notification System** - Real-time notifications
8. **Administrator** - User and system management

### Backend Modules ([backend-todos.md](mdc:tasks/backend-todos.md))
1. **MetaMask Integration** - Blockchain authentication services
2. **User Center** - Profile and wallet management APIs
3. **Patent Management** - CRUD operations with blockchain storage
4. **Patent Trading** - Smart contract-based trading system
5. **Rights Protection** - Patent rights claim processing
6. **Audit Management** - Audit workflow APIs
7. **Notification System** - Notification management APIs
8. **Administrator** - Admin APIs for user and system management
9. **IPFS Integration** - Document storage and retrieval
10. **Smart Contracts** - Core blockchain contracts
11. **Blockchain Infrastructure** - Ganache integration and management

## Key Technologies
- **Blockchain**: Ganache for local development, smart contracts for business logic
- **Authentication**: MetaMask wallet-based authentication
- **File Storage**: IPFS for patent documents
- **Real-time**: Event-driven notifications from blockchain
- **Security**: Blockchain-based access control and validation
