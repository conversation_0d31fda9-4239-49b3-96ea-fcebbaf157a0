---
description:
globs:
alwaysApply: false
---
# Frontend Structure and Development Guidelines

## Component Organization
Based on [frontend-todos.md](mdc:tasks/frontend-todos.md), organize Vue.js components by feature modules:

### Core Components Structure
```
src/
├── components/
│   ├── MetaMask/
│   │   ├── MetaMaskConnector.vue
│   │   └── NetworkValidator.vue
│   ├── User/
│   │   ├── UserProfile.vue
│   │   ├── Wallet.vue
│   │   ├── RechargeModal.vue
│   │   ├── WithdrawModal.vue
│   │   └── TransactionHistory.vue
│   ├── Patent/
│   │   ├── PatentUpload.vue
│   │   ├── PatentSearch.vue
│   │   ├── PatentList.vue
│   │   ├── PatentCard.vue
│   │   ├── PatentDetail.vue
│   │   ├── PatentDocuments.vue
│   │   ├── PatentEvents.vue
│   │   ├── TradeButton.vue
│   │   └── MyPatents.vue
│   ├── Trading/
│   │   ├── PatentTrade.vue
│   │   ├── TradeConfirmation.vue
│   │   └── TradeContract.vue
│   ├── RightsProtection/
│   │   ├── RightsProtection.vue
│   │   └── RightsEvidenceUpload.vue
│   ├── Audit/
│   │   ├── AuditPending.vue
│   │   ├── PatentAuditCard.vue
│   │   ├── TradeAuditCard.vue
│   │   ├── RightsAuditCard.vue
│   │   ├── AuditForm.vue
│   │   └── AuditHistory.vue
│   ├── Notification/
│   │   ├── NotificationCenter.vue
│   │   ├── NotificationItem.vue
│   │   └── NotificationFilter.vue
│   └── Admin/
│       ├── UserManagement.vue
│       ├── UserTable.vue
│       ├── UserDetailModal.vue
│       ├── PermissionEditor.vue
│       ├── SystemSettings.vue
│       └── ConfigEditor.vue
```

## Routing Structure
Implement route organization matching component structure:

### Core Routes
- `/profile` - User profile management
- `/wallet` - Wallet functionality
- `/patent/upload` - Patent upload form
- `/patent/search` - Patent search interface
- `/patent/:id` - Patent detail view
- `/patent/:id/trade` - Patent trading interface
- `/patent/:id/rights-protection` - Rights protection application
- `/my-patents/published` - User's published patents
- `/my-patents/purchased` - User's purchased patents
- `/my-patents/owned` - User's owned patents
- `/audit/pending` - Pending audit items (admin only)
- `/audit/history` - Audit history (admin only)
- `/notifications` - Notification center
- `/admin/users` - User management (admin only)
- `/admin/settings` - System settings (admin only)

## MetaMask Integration Patterns

### Wallet Connection Flow
1. **Detection**: Check if MetaMask is installed
2. **Connection**: Request wallet connection
3. **Network Validation**: Verify Ganache network connection
4. **Account Monitoring**: Listen for account changes
5. **Authentication**: Validate user registration on blockchain

### State Management
- Store wallet connection status globally
- Track current blockchain address
- Monitor network changes
- Handle authentication state

## Component Development Guidelines

### Modal Components
- Use consistent modal structure for actions requiring user confirmation
- Implement proper error handling and loading states
- Include blockchain transaction status feedback

### Form Components
- Validate input data before blockchain submission
- Show transaction confirmation modals
- Handle MetaMask transaction approval flow
- Display gas fees and transaction costs

### List/Table Components  
- Implement pagination for blockchain data queries
- Use consistent card layouts for patent displays
- Include loading states for blockchain operations
- Handle empty states gracefully

### File Upload Components
- Support multiple formats (Word, PDF, PNG)
- Integrate with IPFS upload functionality
- Show upload progress and IPFS hash results
- Validate file types and sizes before upload
