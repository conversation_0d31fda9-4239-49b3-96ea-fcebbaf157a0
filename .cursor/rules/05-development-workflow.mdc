---
description:
globs:
alwaysApply: false
---
# Development Workflow and Standards

## Project Setup and Structure
Based on [frontend-todos.md](mdc:tasks/frontend-todos.md) and [backend-todos.md](mdc:tasks/backend-todos.md), establish proper project structure:

### Repository Structure
```
blockchain-patent-exchange/
├── frontend/                    # Vue.js application
│   ├── src/
│   │   ├── components/         # Vue components by feature
│   │   ├── views/              # Page components
│   │   ├── services/           # API and blockchain services
│   │   ├── store/              # Vuex store modules
│   │   ├── router/             # Vue Router configuration
│   │   └── utils/              # Utility functions
│   ├── public/
│   └── package.json
├── backend/                     # Node.js/Express API
│   ├── src/
│   │   ├── routes/             # API routes by feature
│   │   ├── middleware/         # Custom middleware
│   │   ├── services/           # Business logic services
│   │   ├── contracts/          # Smart contract ABIs
│   │   └── utils/              # Utility functions
│   ├── contracts/              # Solidity smart contracts
│   ├── migrations/             # Truffle migrations
│   └── package.json
├── docs/                       # Documentation
├── tests/                      # Integration and E2E tests
└── README.md
```

## Development Environment Setup

### Prerequisites
1. **Node.js** (v16+) for frontend and backend development
2. **Ganache** for local blockchain development
3. **Truffle** for smart contract development and deployment
4. **IPFS** node for document storage
5. **MetaMask** browser extension for wallet integration

### Initial Setup Commands
```bash
# Clone repository
git clone <repository-url>
cd blockchain-patent-exchange

# Setup backend
cd backend
npm install
npm run compile-contracts  # Compile smart contracts
npm run migrate            # Deploy contracts to Ganache
npm run dev               # Start development server

# Setup frontend (in new terminal)
cd frontend
npm install
npm run serve             # Start Vue.js development server

# Setup IPFS node
ipfs daemon               # Start IPFS daemon
```

## Coding Standards and Conventions

### Frontend Standards (Vue.js)
- **Component Naming**: Use PascalCase for component names (e.g., `PatentUpload.vue`)
- **File Organization**: Group components by feature modules as shown in [frontend-todos.md](mdc:tasks/frontend-todos.md)
- **Props Validation**: Always define prop types and validation
- **Event Naming**: Use kebab-case for custom events (e.g., `patent-uploaded`)
- **State Management**: Use Vuex for global state, local state for component-specific data
- **API Calls**: Centralize API calls in service files
- **Error Handling**: Implement consistent error handling and user feedback

### Backend Standards (Node.js/Express)
- **Route Organization**: Group routes by feature as outlined in [backend-todos.md](mdc:tasks/backend-todos.md)
- **Middleware**: Use middleware for authentication, validation, and error handling
- **Async/Await**: Use async/await for asynchronous operations
- **Error Handling**: Implement centralized error handling middleware
- **Validation**: Use validation libraries for input validation
- **Logging**: Implement comprehensive logging for debugging and monitoring

### Smart Contract Standards (Solidity)
- **Version**: Use Solidity ^0.8.0 for built-in overflow protection
- **Naming**: Use PascalCase for contract names, camelCase for functions
- **Access Control**: Implement proper access modifiers (public, private, internal)
- **Events**: Emit events for important state changes
- **Gas Optimization**: Optimize functions for gas efficiency
- **Security**: Follow security best practices and use established patterns

## Development Workflow

### Feature Development Process
1. **Create Feature Branch**: `git checkout -b feature/patent-upload`
2. **Backend Development**: Implement API endpoints and smart contract functions
3. **Frontend Development**: Create Vue components and integrate with backend
4. **Testing**: Write and run unit tests, integration tests
5. **Code Review**: Submit pull request for code review
6. **Deployment**: Deploy to development environment for testing
7. **Merge**: Merge to main branch after approval

### Smart Contract Development Workflow
1. **Write Contract**: Develop Solidity smart contract
2. **Compile**: Use Truffle to compile contracts
3. **Test**: Write comprehensive tests for contract functions
4. **Deploy**: Deploy to Ganache for development testing
5. **Integration**: Update backend services to use new contract
6. **Frontend Integration**: Update frontend to interact with new contract functions

### API Development Workflow
1. **Design API**: Define endpoints based on frontend requirements
2. **Implement Routes**: Create Express.js routes with proper middleware
3. **Add Validation**: Implement input validation and error handling
4. **Test Endpoints**: Use Postman or automated tests to verify functionality
5. **Document API**: Update API documentation with new endpoints
6. **Frontend Integration**: Update frontend services to use new endpoints

## Testing Strategy

### Frontend Testing
- **Unit Tests**: Test individual Vue components using Vue Test Utils
- **Integration Tests**: Test component interactions and data flow
- **E2E Tests**: Test complete user workflows with Cypress
- **MetaMask Testing**: Test wallet connection and transaction flows

### Backend Testing
- **Unit Tests**: Test individual functions and middleware
- **Integration Tests**: Test API endpoints and database interactions
- **Blockchain Tests**: Test smart contract interactions
- **Security Tests**: Test authentication and authorization

### Smart Contract Testing
- **Unit Tests**: Test individual contract functions with Truffle
- **Integration Tests**: Test contract interactions and workflows
- **Security Tests**: Test for common vulnerabilities (reentrancy, overflow)
- **Gas Tests**: Optimize and test gas usage

## Code Quality and Maintenance

### Code Review Guidelines
- **Security**: Review for security vulnerabilities and best practices
- **Performance**: Check for performance optimizations
- **Standards**: Ensure code follows established conventions
- **Testing**: Verify adequate test coverage
- **Documentation**: Check for proper code documentation

### Continuous Integration
- **Automated Testing**: Run all tests on every commit
- **Code Quality**: Use linting tools for code quality checks
- **Security Scanning**: Automated security vulnerability scanning
- **Build Verification**: Ensure all components build successfully

### Deployment Guidelines
- **Environment Configuration**: Separate configurations for development, staging, production
- **Contract Deployment**: Use migration scripts for contract deployment
- **Database Migration**: Handle data migration and schema changes
- **Monitoring**: Implement logging and monitoring for production environment

## Documentation Standards

### Code Documentation
- **Inline Comments**: Document complex business logic and blockchain interactions
- **Function Documentation**: Document parameters, return values, and side effects
- **API Documentation**: Maintain up-to-date API documentation
- **Smart Contract Documentation**: Document contract functions and events

### User Documentation
- **Setup Guide**: Comprehensive development environment setup
- **User Guide**: End-user documentation for system usage
- **Admin Guide**: Administrator documentation for system management
- **Troubleshooting**: Common issues and solutions

## Security Considerations

### Development Security
- **Environment Variables**: Use environment variables for sensitive configuration
- **Access Control**: Implement proper authentication and authorization
- **Input Validation**: Validate all user inputs and API parameters
- **Error Handling**: Avoid exposing sensitive information in error messages

### Blockchain Security
- **Private Keys**: Never commit private keys or sensitive data
- **Smart Contract Security**: Follow security best practices for contract development
- **Transaction Security**: Implement proper transaction validation
- **Access Control**: Use role-based access control in smart contracts
