/**
 * 专利管理相关 API 服务
 */

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'

class PatentService {
  /**
   * 上传专利文档到 IPFS
   * @param {File} file - 专利文档文件
   * @returns {Promise<string>} IPFS 哈希值
   */
  async uploadToIPFS(file) {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch(`${API_BASE_URL}/ipfs/upload`, {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data.hash
    } catch (error) {
      console.error('上传文件到 IPFS 失败:', error)
      throw error
    }
  }

  /**
   * 创建专利
   * @param {Object} patentData - 专利信息
   * @returns {Promise<Object>} 创建结果
   */
  async createPatent(patentData) {
    try {
      const response = await fetch(`${API_BASE_URL}/patents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(patentData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('创建专利失败:', error)
      throw error
    }
  }

  /**
   * 搜索专利
   * @param {Object} searchParams - 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  async searchPatents(searchParams = {}) {
    try {
      const queryParams = new URLSearchParams()
      
      if (searchParams.keyword) queryParams.append('keyword', searchParams.keyword)
      if (searchParams.patentNumber) queryParams.append('patentNumber', searchParams.patentNumber)
      if (searchParams.category) queryParams.append('category', searchParams.category)
      if (searchParams.searchType) queryParams.append('searchType', searchParams.searchType)
      if (searchParams.page) queryParams.append('page', searchParams.page)
      if (searchParams.limit) queryParams.append('limit', searchParams.limit)

      const url = `${API_BASE_URL}/patents?${queryParams.toString()}`
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('搜索专利失败:', error)
      throw error
    }
  }

  /**
   * 获取专利详情
   * @param {number} patentId - 专利ID
   * @returns {Promise<Object>} 专利详情
   */
  async getPatentDetail(patentId) {
    try {
      const response = await fetch(`${API_BASE_URL}/patents/${patentId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取专利详情失败:', error)
      throw error
    }
  }

  /**
   * 获取专利分类列表
   * @returns {Promise<Array>} 分类列表
   */
  async getPatentCategories() {
    try {
      const response = await fetch(`${API_BASE_URL}/patents/categories`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取专利分类失败:', error)
      throw error
    }
  }

  /**
   * 获取专利事件历史
   * @param {number} patentId - 专利ID
   * @returns {Promise<Array>} 事件历史列表
   */
  async getPatentEvents(patentId) {
    try {
      const response = await fetch(`${API_BASE_URL}/patents/${patentId}/events`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取专利事件历史失败:', error)
      throw error
    }
  }

  /**
   * 获取我发布的专利
   * @param {string} address - 用户地址
   * @returns {Promise<Array>} 专利列表
   */
  async getMyPublishedPatents(address) {
    try {
      const response = await fetch(`${API_BASE_URL}/patents/my-published/${address}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取我发布的专利失败:', error)
      throw error
    }
  }

  /**
   * 获取我购买的专利
   * @param {string} address - 用户地址
   * @returns {Promise<Array>} 专利列表
   */
  async getMyPurchasedPatents(address) {
    try {
      const response = await fetch(`${API_BASE_URL}/patents/my-purchased/${address}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取我购买的专利失败:', error)
      throw error
    }
  }

  /**
   * 获取我拥有的专利
   * @param {string} address - 用户地址
   * @returns {Promise<Array>} 专利列表
   */
  async getMyOwnedPatents(address) {
    try {
      const response = await fetch(`${API_BASE_URL}/patents/my-owned/${address}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取我拥有的专利失败:', error)
      throw error
    }
  }

  /**
   * 更新专利状态
   * @param {number} patentId - 专利ID
   * @param {string} status - 新状态
   * @param {string} address - 用户地址
   * @returns {Promise<Object>} 更新结果
   */
  async updatePatentStatus(patentId, status, address) {
    try {
      const response = await fetch(`${API_BASE_URL}/patents/${patentId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, address }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('更新专利状态失败:', error)
      throw error
    }
  }

  /**
   * 重新上架专利
   * @param {number} patentId - 专利ID
   * @param {number} price - 新价格
   * @param {string} address - 用户地址
   * @returns {Promise<Object>} 更新结果
   */
  async relistPatent(patentId, price, address) {
    try {
      const response = await fetch(`${API_BASE_URL}/patents/${patentId}/relist`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ price, address }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('重新上架专利失败:', error)
      throw error
    }
  }

  /**
   * 从 IPFS 下载文件
   * @param {string} hash - IPFS 哈希值
   * @returns {Promise<Blob>} 文件内容
   */
  async downloadFromIPFS(hash) {
    try {
      const response = await fetch(`${API_BASE_URL}/ipfs/${hash}/download`, {
        method: 'GET',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.blob()
    } catch (error) {
      console.error('从 IPFS 下载文件失败:', error)
      throw error
    }
  }
}

// 创建单例实例
const patentService = new PatentService()

export default patentService
