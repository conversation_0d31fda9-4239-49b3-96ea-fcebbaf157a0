/**
 * 用户中心相关 API 服务
 */

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'

class UserService {
  /**
   * 获取用户资料
   * @param {string} address - 用户钱包地址
   * @returns {Promise<Object>} 用户资料信息
   */
  async getUserProfile(address) {
    try {
      const response = await fetch(`${API_BASE_URL}/users/${address}/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('获取用户资料失败:', error)
      throw error
    }
  }

  /**
   * 更新用户资料
   * @param {string} address - 用户钱包地址
   * @param {Object} profileData - 用户资料数据
   * @returns {Promise<Object>} 更新结果
   */
  async updateUserProfile(address, profileData) {
    try {
      const response = await fetch(`${API_BASE_URL}/users/${address}/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw error
    }
  }

  /**
   * 检查用户是否为首次使用
   * @param {string} address - 用户钱包地址
   * @returns {Promise<boolean>} 是否为首次使用
   */
  async isFirstTimeUser(address) {
    try {
      const response = await fetch(`${API_BASE_URL}/users/${address}/first-time`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      return data.isFirstTime
    } catch (error) {
      console.error('检查首次使用状态失败:', error)
      throw error
    }
  }

  /**
   * 获取用户钱包余额
   * @param {string} address - 用户钱包地址
   * @returns {Promise<string>} 钱包余额 (ETH)
   */
  async getWalletBalance(address) {
    try {
      const response = await fetch(`${API_BASE_URL}/wallet/${address}/balance`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      return data.balance
    } catch (error) {
      console.error('获取钱包余额失败:', error)
      throw error
    }
  }

  /**
   * 获取交易历史记录
   * @param {string} address - 用户钱包地址
   * @param {Object} options - 查询选项 (limit, offset, type, timeRange)
   * @returns {Promise<Array>} 交易记录列表
   */
  async getTransactionHistory(address, options = {}) {
    try {
      const queryParams = new URLSearchParams()
      
      if (options.limit) queryParams.append('limit', options.limit)
      if (options.offset) queryParams.append('offset', options.offset)
      if (options.type) queryParams.append('type', options.type)
      if (options.timeRange) queryParams.append('timeRange', options.timeRange)
      if (options.keyword) queryParams.append('keyword', options.keyword)
      
      const url = `${API_BASE_URL}/wallet/${address}/transactions?${queryParams.toString()}`
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('获取交易历史失败:', error)
      throw error
    }
  }

  /**
   * 模拟充值操作
   * @param {string} address - 用户钱包地址
   * @param {string} amount - 充值金额 (ETH)
   * @returns {Promise<Object>} 充值结果
   */
  async simulateRecharge(address, amount) {
    try {
      const response = await fetch(`${API_BASE_URL}/wallet/${address}/recharge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount }),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('模拟充值失败:', error)
      throw error
    }
  }

  /**
   * 模拟提现操作
   * @param {string} address - 用户钱包地址
   * @param {string} amount - 提现金额 (ETH)
   * @returns {Promise<Object>} 提现结果
   */
  async simulateWithdraw(address, amount) {
    try {
      const response = await fetch(`${API_BASE_URL}/wallet/${address}/withdraw`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount }),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('模拟提现失败:', error)
      throw error
    }
  }

  /**
   * 获取交易详情
   * @param {string} transactionId - 交易ID
   * @returns {Promise<Object>} 交易详情
   */
  async getTransactionDetail(transactionId) {
    try {
      const response = await fetch(`${API_BASE_URL}/transactions/${transactionId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error('获取交易详情失败:', error)
      throw error
    }
  }
}

// 创建单例实例
const userService = new UserService()

export default userService
