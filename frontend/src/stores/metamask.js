import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import Web3 from 'web3'

export const useMetaMaskStore = defineStore('metamask', () => {
  // Reactive state
  const isConnected = ref(false)
  const currentAccount = ref('')
  const accounts = ref([])
  const chainId = ref('')
  const networkName = ref('')
  const balance = ref('0')
  const isMetaMaskInstalled = ref(false)
  const web3 = ref(null)
  const isConnecting = ref(false)
  const error = ref('')

  // Ganache local network configuration
  const GANACHE_CHAIN_ID = '0x539' // 1337 in hex
  const GANACHE_NETWORK_CONFIG = {
    chainId: GANACHE_CHAIN_ID,
    chainName: 'Ganache Local',
    rpcUrls: ['http://localhost:7545'],
    nativeCurrency: {
      name: 'ETH',
      symbol: 'ETH',
      decimals: 18
    }
  }

  // Computed properties
  const isGanacheNetwork = computed(() => chainId.value === GANACHE_CHAIN_ID)
  const shortAddress = computed(() => {
    if (!currentAccount.value) return ''
    return `${currentAccount.value.slice(0, 6)}...${currentAccount.value.slice(-4)}`
  })

  // Check if MetaMask is installed
  function checkMetaMaskInstalled() {
    isMetaMaskInstalled.value = typeof window !== 'undefined' && typeof window.ethereum !== 'undefined'
    return isMetaMaskInstalled.value
  }

  // Initialize Web3 instance
  function initWeb3() {
    if (window.ethereum) {
      web3.value = new Web3(window.ethereum)
      return true
    }
    return false
  }

  // Connect to MetaMask
  async function connect() {
    if (!checkMetaMaskInstalled()) {
      error.value = '请安装 MetaMask 钱包'
      return false
    }

    isConnecting.value = true
    error.value = ''

    try {
      // Request account access
      const accountsList = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })

      if (accountsList.length > 0) {
        accounts.value = accountsList
        currentAccount.value = accountsList[0]
        isConnected.value = true

        // Initialize Web3
        initWeb3()

        // Get network info
        await updateNetworkInfo()

        // Get balance
        await updateBalance()

        // Setup event listeners
        setupEventListeners()

        return true
      }
    } catch (err) {
      console.error('连接 MetaMask 失败:', err)
      error.value = err.message || '连接失败'
    } finally {
      isConnecting.value = false
    }

    return false
  }

  // Disconnect from MetaMask
  function disconnect() {
    isConnected.value = false
    currentAccount.value = ''
    accounts.value = []
    chainId.value = ''
    networkName.value = ''
    balance.value = '0'
    web3.value = null
    error.value = ''
  }

  // Update network information
  async function updateNetworkInfo() {
    if (!window.ethereum) return

    try {
      const chain = await window.ethereum.request({ method: 'eth_chainId' })
      chainId.value = chain

      // Set network name based on chain ID
      switch (chain) {
        case '0x1':
          networkName.value = 'Ethereum 主网'
          break
        case '0x3':
          networkName.value = 'Ropsten 测试网'
          break
        case '0x4':
          networkName.value = 'Rinkeby 测试网'
          break
        case '0x5':
          networkName.value = 'Goerli 测试网'
          break
        case '0x539':
          networkName.value = 'Ganache 本地网络'
          break
        default:
          networkName.value = `未知网络 (${chain})`
      }
    } catch (err) {
      console.error('获取网络信息失败:', err)
    }
  }

  // Update account balance
  async function updateBalance() {
    if (!web3.value || !currentAccount.value) return

    try {
      const balanceWei = await web3.value.eth.getBalance(currentAccount.value)
      balance.value = web3.value.utils.fromWei(balanceWei, 'ether')
    } catch (err) {
      console.error('获取余额失败:', err)
    }
  }

  // Switch to Ganache network
  async function switchToGanache() {
    if (!window.ethereum) return false

    try {
      // Try to switch to Ganache
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: GANACHE_CHAIN_ID }]
      })
      return true
    } catch (switchError) {
      // If network doesn't exist, add it
      if (switchError.code === 4902) {
        try {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [GANACHE_NETWORK_CONFIG]
          })
          return true
        } catch (addError) {
          console.error('添加 Ganache 网络失败:', addError)
          error.value = '无法添加 Ganache 网络'
          return false
        }
      }
      console.error('切换到 Ganache 网络失败:', switchError)
      error.value = '无法切换到 Ganache 网络'
      return false
    }
  }

  // Setup event listeners for MetaMask events
  function setupEventListeners() {
    if (!window.ethereum) return

    // Account changed
    window.ethereum.on('accountsChanged', (accountsList) => {
      if (accountsList.length === 0) {
        disconnect()
      } else {
        accounts.value = accountsList
        currentAccount.value = accountsList[0]
        updateBalance()
      }
    })

    // Network changed
    window.ethereum.on('chainChanged', () => {
      updateNetworkInfo()
      updateBalance()
    })

    // Connection lost
    window.ethereum.on('disconnect', () => {
      disconnect()
    })
  }

  // Initialize on store creation
  function init() {
    checkMetaMaskInstalled()
    
    // Check if already connected
    if (window.ethereum && window.ethereum.selectedAddress) {
      currentAccount.value = window.ethereum.selectedAddress
      isConnected.value = true
      initWeb3()
      updateNetworkInfo()
      updateBalance()
      setupEventListeners()
    }
  }

  return {
    // State
    isConnected,
    currentAccount,
    accounts,
    chainId,
    networkName,
    balance,
    isMetaMaskInstalled,
    isConnecting,
    error,
    
    // Computed
    isGanacheNetwork,
    shortAddress,
    
    // Actions
    checkMetaMaskInstalled,
    connect,
    disconnect,
    updateNetworkInfo,
    updateBalance,
    switchToGanache,
    init
  }
}) 