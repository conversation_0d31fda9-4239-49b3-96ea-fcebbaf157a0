# User Center Module - 用户中心模块

## 概述

用户中心模块是区块链专利交易系统的核心功能模块之一，提供用户个人信息管理和钱包功能。

## 功能特性

### 2.1 个人信息管理
- ✅ 用户资料管理 (UserProfile.vue)
- ✅ 区块链地址显示 (只读)
- ✅ 个人信息表单 (姓名、手机、身份证)
- ✅ 首次用户信息完善要求
- ✅ 区块链交易确认机制
- ✅ API 集成支持

### 2.2 钱包功能
- ✅ 钱包主界面 (Wallet.vue)
- ✅ 智能合约余额显示
- ✅ 充值功能 (RechargeModal.vue)
- ✅ 提现功能 (WithdrawModal.vue)
- ✅ 交易历史记录 (TransactionHistory.vue)
- ✅ 模拟充值/提现功能
- ✅ 交易记录筛选和分页
- ✅ API 集成支持

## 文件结构

```
frontend/src/
├── views/
│   ├── UserProfile.vue          # 用户资料页面
│   └── Wallet.vue               # 钱包页面
├── components/
│   ├── RechargeModal.vue        # 充值模态框
│   ├── WithdrawModal.vue        # 提现模态框
│   └── TransactionHistory.vue   # 交易历史组件
├── composables/
│   ├── useUserProfile.js        # 用户资料管理 Composable
│   └── useWallet.js             # 钱包功能管理 Composable
├── services/
│   └── userService.js           # 用户相关 API 服务
└── router/
    └── index.js                 # 路由配置
```

## 路由配置

- `/profile` - 个人资料页面
- `/wallet` - 钱包页面

## API 端点

### 用户资料相关
- `GET /api/users/{address}/profile` - 获取用户资料
- `PUT /api/users/{address}/profile` - 更新用户资料
- `GET /api/users/{address}/first-time` - 检查首次使用状态

### 钱包相关
- `GET /api/wallet/{address}/balance` - 获取钱包余额
- `GET /api/wallet/{address}/transactions` - 获取交易记录
- `POST /api/wallet/{address}/recharge` - 模拟充值
- `POST /api/wallet/{address}/withdraw` - 模拟提现
- `GET /api/transactions/{id}` - 获取交易详情

## 使用方法

### 1. 用户资料管理

```vue
<script>
import { useUserProfile } from '@/composables/useUserProfile'

export default {
  setup() {
    const {
      profileForm,
      errors,
      isFirstTimeUser,
      loadProfile,
      saveProfile,
      resetForm
    } = useUserProfile()
    
    return {
      profileForm,
      errors,
      isFirstTimeUser,
      loadProfile,
      saveProfile,
      resetForm
    }
  }
}
</script>
```

### 2. 钱包功能

```vue
<script>
import { useWallet } from '@/composables/useWallet'

export default {
  setup() {
    const {
      balance,
      formattedBalance,
      transactions,
      loadBalance,
      simulateRecharge,
      simulateWithdraw
    } = useWallet()
    
    return {
      balance,
      formattedBalance,
      transactions,
      loadBalance,
      simulateRecharge,
      simulateWithdraw
    }
  }
}
</script>
```

## 特性说明

### 1. 响应式设计
- 支持移动端和桌面端
- Bootstrap 5 样式框架
- 自适应布局

### 2. 错误处理
- API 调用失败时自动降级到模拟数据
- 表单验证和错误提示
- 用户友好的错误消息

### 3. 状态管理
- 使用 Vue 3 Composition API
- 响应式状态管理
- 自动同步 MetaMask 状态

### 4. 安全性
- 区块链地址验证
- 表单数据验证
- 安全的 API 调用

## 开发注意事项

1. **API 集成**: 当前实现包含 API 调用和模拟数据降级机制
2. **MetaMask 依赖**: 功能依赖 MetaMask 连接状态
3. **区块链确认**: 重要操作需要区块链交易确认
4. **数据验证**: 所有用户输入都经过严格验证

## 测试建议

1. 测试 MetaMask 连接和断开场景
2. 测试表单验证和错误处理
3. 测试 API 调用成功和失败场景
4. 测试响应式布局在不同设备上的表现
5. 测试交易历史的筛选和分页功能

## 后续扩展

- 添加更多钱包功能 (如资产统计)
- 集成真实的智能合约调用
- 添加更多用户设置选项
- 实现实时通知功能
