<template>
  <div class="container mt-4">
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <!-- 页面标题 -->
        <div class="d-flex align-items-center mb-4">
          <button class="btn btn-outline-secondary me-3" @click="goBack">
            <i class="bi bi-arrow-left"></i>
          </button>
          <h2 class="mb-0">
            <i class="bi bi-currency-exchange me-2"></i>
            专利交易
          </h2>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2">正在加载专利信息...</p>
        </div>

        <!-- 专利信息卡片 -->
        <div v-else-if="patent" class="card mb-4">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
              <i class="bi bi-file-earmark-text me-2"></i>
              专利信息
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <h4 class="text-primary">{{ patent.name }}</h4>
                <p class="text-muted mb-2">专利号: {{ patent.number }}</p>
                <p class="mb-2">
                  <span class="badge bg-info me-2">{{ patent.category }}</span>
                  <span class="badge bg-success">{{ patent.status }}</span>
                </p>
                <p class="mb-0">{{ patent.summary }}</p>
              </div>
              <div class="col-md-4 text-end">
                <div class="price-display">
                  <h3 class="text-success mb-0">{{ patent.price }} ETH</h3>
                  <small class="text-muted">交易价格</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 卖方信息 -->
        <div v-if="patent" class="card mb-4">
          <div class="card-header bg-info text-white">
            <h6 class="mb-0">
              <i class="bi bi-person me-2"></i>
              卖方信息
            </h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <p><strong>姓名:</strong> {{ patent.owner.name }}</p>
                <p><strong>钱包地址:</strong> {{ formatAddress(patent.owner.address) }}</p>
              </div>
              <div class="col-md-6">
                <p><strong>联系电话:</strong> {{ patent.owner.phone || '未提供' }}</p>
                <p><strong>上传时间:</strong> {{ formatDate(patent.uploadTime) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 交易确认表单 -->
        <div v-if="patent" class="card mb-4">
          <div class="card-header bg-warning text-dark">
            <h6 class="mb-0">
              <i class="bi bi-check-circle me-2"></i>
              交易确认
            </h6>
          </div>
          <div class="card-body">
            <!-- 钱包余额检查 -->
            <div class="alert alert-info d-flex align-items-center mb-3">
              <i class="bi bi-wallet2 me-2"></i>
              <div>
                <strong>当前钱包余额:</strong> {{ walletBalance }} ETH
                <span v-if="parseFloat(walletBalance) < parseFloat(patent.price)" class="text-danger ms-2">
                  (余额不足)
                </span>
              </div>
            </div>

            <!-- 交易详情 -->
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="border rounded p-3">
                  <h6 class="text-muted mb-2">交易金额</h6>
                  <h4 class="text-primary">{{ patent.price }} ETH</h4>
                </div>
              </div>
              <div class="col-md-6">
                <div class="border rounded p-3">
                  <h6 class="text-muted mb-2">预估手续费</h6>
                  <h4 class="text-secondary">{{ estimatedGasFee }} ETH</h4>
                </div>
              </div>
            </div>

            <!-- 风险提示 -->
            <div class="alert alert-warning">
              <h6><i class="bi bi-exclamation-triangle me-2"></i>重要提示</h6>
              <ul class="mb-0">
                <li>区块链交易一旦确认无法撤销，请仔细核对交易信息</li>
                <li>交易完成后，专利所有权将自动转移至您的账户</li>
                <li>请确保您的钱包有足够的余额支付交易金额和手续费</li>
                <li>交易可能需要几分钟时间完成确认</li>
              </ul>
            </div>

            <!-- 确认复选框 -->
            <div class="form-check mb-3">
              <input 
                class="form-check-input" 
                type="checkbox" 
                id="confirmTrade" 
                v-model="confirmed"
              >
              <label class="form-check-label" for="confirmTrade">
                我已仔细阅读并同意上述条款，确认进行此次专利交易
              </label>
            </div>

            <!-- 交易按钮 -->
            <div class="d-grid gap-2">
              <button 
                class="btn btn-success btn-lg"
                @click="initiateTrade"
                :disabled="!canTrade || trading"
              >
                <i class="bi bi-currency-exchange me-2"></i>
                {{ trading ? '交易进行中...' : '确认交易' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-else-if="error" class="alert alert-danger">
          <h5><i class="bi bi-exclamation-triangle me-2"></i>加载失败</h5>
          <p class="mb-0">{{ error }}</p>
          <button class="btn btn-outline-danger mt-2" @click="loadPatent">
            <i class="bi bi-arrow-clockwise me-1"></i>
            重试
          </button>
        </div>
      </div>
    </div>

    <!-- 交易确认模态框 -->
    <TradeConfirmation 
      v-if="showConfirmation"
      :patent="patent"
      :trade-data="tradeData"
      @confirm="executeTrade"
      @cancel="cancelTrade"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useWallet } from '@/composables/useWallet'
import TradeConfirmation from '@/components/TradeConfirmation.vue'

export default {
  name: 'PatentTrade',
  components: {
    TradeConfirmation
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const { getBalance } = useWallet()

    const loading = ref(false)
    const trading = ref(false)
    const error = ref(null)
    const patent = ref(null)
    const walletBalance = ref('0')
    const estimatedGasFee = ref('0.001')
    const confirmed = ref(false)
    const showConfirmation = ref(false)
    const tradeData = ref(null)

    // 计算是否可以交易
    const canTrade = computed(() => {
      return confirmed.value && 
             parseFloat(walletBalance.value) >= parseFloat(patent.value?.price || 0) + parseFloat(estimatedGasFee.value)
    })

    // 加载专利信息
    const loadPatent = async () => {
      const patentId = route.params.id
      if (!patentId) {
        error.value = '无效的专利ID'
        return
      }

      loading.value = true
      error.value = null

      try {
        // 模拟 API 调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟专利数据
        patent.value = {
          id: patentId,
          name: '一种新型区块链共识算法',
          number: 'CN202100001234.5',
          category: '发明专利',
          status: '可交易',
          price: '10.5',
          summary: '本发明提出了一种基于权益证明的新型区块链共识算法，能够有效提高交易处理速度并降低能耗。',
          owner: {
            name: '张三',
            address: '******************************************',
            phone: '13800138000'
          },
          uploadTime: '2023-06-15T10:30:00Z'
        }

        // 加载钱包余额
        walletBalance.value = await getBalance()
      } catch (err) {
        error.value = '加载专利信息失败，请重试'
        console.error('加载专利失败:', err)
      } finally {
        loading.value = false
      }
    }

    // 发起交易
    const initiateTrade = () => {
      if (!canTrade.value) return

      tradeData.value = {
        patentId: patent.value.id,
        price: patent.value.price,
        gasFee: estimatedGasFee.value,
        total: (parseFloat(patent.value.price) + parseFloat(estimatedGasFee.value)).toString()
      }

      showConfirmation.value = true
    }

    // 执行交易
    const executeTrade = async (transactionData) => {
      trading.value = true
      showConfirmation.value = false

      try {
        // 模拟区块链交易
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        // 交易成功，跳转到交易结果页面
        router.push({
          name: 'trade-result',
          params: { 
            patentId: patent.value.id,
            transactionHash: '0xabcdef1234567890abcdef1234567890abcdef12'
          }
        })
      } catch (err) {
        console.error('交易失败:', err)
        alert('交易失败，请重试')
      } finally {
        trading.value = false
      }
    }

    // 取消交易
    const cancelTrade = () => {
      showConfirmation.value = false
      tradeData.value = null
    }

    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }

    // 格式化地址
    const formatAddress = (address) => {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }

    onMounted(() => {
      loadPatent()
    })

    return {
      loading,
      trading,
      error,
      patent,
      walletBalance,
      estimatedGasFee,
      confirmed,
      showConfirmation,
      tradeData,
      canTrade,
      loadPatent,
      initiateTrade,
      executeTrade,
      cancelTrade,
      goBack,
      formatAddress,
      formatDate
    }
  }
}
</script>

<style scoped>
.price-display {
  text-align: center;
  padding: 1rem;
  border: 2px solid #28a745;
  border-radius: 0.5rem;
  background-color: #f8f9fa;
}

.card-header h6 {
  font-weight: 600;
}

.alert ul {
  padding-left: 1.2rem;
}

.form-check-label {
  font-size: 0.95rem;
  line-height: 1.4;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}
</style>
