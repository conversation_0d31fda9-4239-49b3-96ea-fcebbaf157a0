<template>
  <div class="patent-upload">
    <div class="container mt-4">
      <div class="row justify-content-center">
        <div class="col-lg-8">
          <div class="card">
            <div class="card-header">
              <h4 class="mb-0">
                <i class="bi bi-upload"></i>
                专利上传
              </h4>
            </div>
            <div class="card-body">
              <form @submit.prevent="submitPatent">
                <!-- 专利基本信息 -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary">专利基本信息</h5>
                    <hr>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="patentName" class="form-label">专利名称 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="patentName"
                      v-model="formData.patentName"
                      required
                      placeholder="请输入专利名称"
                    >
                  </div>
                  <div class="col-md-6">
                    <label for="patentNumber" class="form-label">专利号 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="patentNumber"
                      v-model="formData.patentNumber"
                      required
                      placeholder="请输入专利号"
                    >
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="category" class="form-label">专利分类 *</label>
                    <select
                      class="form-select"
                      id="category"
                      v-model="formData.category"
                      required
                    >
                      <option value="">请选择专利分类</option>
                      <option
                        v-for="category in categories"
                        :key="category.value"
                        :value="category.value"
                      >
                        {{ category.label }}
                      </option>
                    </select>
                  </div>
                  <div class="col-md-6">
                    <label for="price" class="form-label">价格 (ETH) *</label>
                    <input
                      type="number"
                      class="form-control"
                      id="price"
                      v-model="formData.price"
                      required
                      min="0"
                      step="0.001"
                      placeholder="请输入价格"
                    >
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="applicationDate" class="form-label">申请日期 *</label>
                    <input
                      type="date"
                      class="form-control"
                      id="applicationDate"
                      v-model="formData.applicationDate"
                      required
                    >
                  </div>
                  <div class="col-md-6">
                    <label for="expirationDate" class="form-label">到期日期 *</label>
                    <input
                      type="date"
                      class="form-control"
                      id="expirationDate"
                      v-model="formData.expirationDate"
                      required
                    >
                  </div>
                </div>

                <div class="mb-3">
                  <label for="summary" class="form-label">专利摘要 *</label>
                  <textarea
                    class="form-control"
                    id="summary"
                    v-model="formData.summary"
                    required
                    rows="4"
                    placeholder="请输入专利摘要"
                  ></textarea>
                </div>

                <!-- 专利权人信息 -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary">专利权人信息</h5>
                    <hr>
                  </div>
                </div>

                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="ownerName" class="form-label">权利人姓名 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="ownerName"
                      v-model="formData.ownerName"
                      required
                      placeholder="请输入权利人姓名"
                    >
                  </div>
                  <div class="col-md-6">
                    <label for="ownerIdCard" class="form-label">身份证号 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="ownerIdCard"
                      v-model="formData.ownerIdCard"
                      required
                      placeholder="请输入身份证号"
                    >
                  </div>
                </div>

                <!-- 代理销售选项 -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary">销售方式</h5>
                    <hr>
                  </div>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="radio"
                      name="saleType"
                      id="directSale"
                      :value="false"
                      v-model="formData.isAgency"
                    >
                    <label class="form-check-label" for="directSale">
                      直接销售
                    </label>
                  </div>
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="radio"
                      name="saleType"
                      id="agencySale"
                      :value="true"
                      v-model="formData.isAgency"
                    >
                    <label class="form-check-label" for="agencySale">
                      代理销售
                    </label>
                  </div>
                </div>

                <!-- 文档上传 -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary">文档上传</h5>
                    <hr>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="patentDocument" class="form-label">专利文档 *</label>
                  <input
                    type="file"
                    class="form-control"
                    id="patentDocument"
                    @change="handlePatentDocumentChange"
                    accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
                    required
                  >
                  <div class="form-text">
                    支持格式：PDF, Word, PNG, JPG (最大 10MB)
                  </div>
                </div>

                <div v-if="formData.isAgency" class="mb-3">
                  <label for="proofDocument" class="form-label">代理证明文档 *</label>
                  <input
                    type="file"
                    class="form-control"
                    id="proofDocument"
                    @change="handleProofDocumentChange"
                    accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
                    :required="formData.isAgency"
                  >
                  <div class="form-text">
                    代理销售需要上传权利人授权证明文档
                  </div>
                </div>

                <!-- 提交按钮 -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                  <button
                    type="button"
                    class="btn btn-secondary me-md-2"
                    @click="resetForm"
                  >
                    重置
                  </button>
                  <button
                    type="submit"
                    class="btn btn-primary"
                    :disabled="isSubmitting"
                  >
                    <span
                      v-if="isSubmitting"
                      class="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    {{ isSubmitting ? '上传中...' : '提交专利' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传进度模态框 -->
    <div
      class="modal fade"
      id="uploadProgressModal"
      tabindex="-1"
      aria-labelledby="uploadProgressModalLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="uploadProgressModalLabel">
              专利上传进度
            </h5>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <div class="d-flex justify-content-between">
                <span>{{ uploadStatus.message }}</span>
                <span>{{ uploadStatus.progress }}%</span>
              </div>
              <div class="progress">
                <div
                  class="progress-bar"
                  role="progressbar"
                  :style="{ width: uploadStatus.progress + '%' }"
                  :aria-valuenow="uploadStatus.progress"
                  aria-valuemin="0"
                  aria-valuemax="100"
                ></div>
              </div>
            </div>
            <div v-if="uploadStatus.error" class="alert alert-danger">
              {{ uploadStatus.error }}
            </div>
          </div>
          <div v-if="uploadStatus.completed" class="modal-footer">
            <button
              type="button"
              class="btn btn-primary"
              data-bs-dismiss="modal"
              @click="handleUploadComplete"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMetaMaskStore } from '@/stores/metamask'
import patentService from '@/services/patentService'
import { Modal } from 'bootstrap'

export default {
  name: 'PatentUpload',
  setup() {
    const router = useRouter()
    const metaMaskStore = useMetaMaskStore()

    // 响应式数据
    const isSubmitting = ref(false)
    const categories = ref([
      { value: 'invention', label: '发明专利' },
      { value: 'utility', label: '实用新型专利' },
      { value: 'design', label: '外观设计专利' },
      { value: 'software', label: '软件著作权' },
      { value: 'trademark', label: '商标' },
    ])

    const formData = reactive({
      patentName: '',
      patentNumber: '',
      category: '',
      price: '',
      applicationDate: '',
      expirationDate: '',
      summary: '',
      ownerName: '',
      ownerIdCard: '',
      isAgency: false,
      patentDocument: null,
      proofDocument: null,
    })

    const uploadStatus = reactive({
      message: '',
      progress: 0,
      error: '',
      completed: false,
    })

    let uploadProgressModal = null

    // 组件挂载时初始化
    onMounted(() => {
      // 检查 MetaMask 连接状态
      if (!metaMaskStore.isConnected) {
        router.push('/')
        return
      }

      // 初始化模态框
      const modalElement = document.getElementById('uploadProgressModal')
      if (modalElement) {
        uploadProgressModal = new Modal(modalElement)
      }
    })

    // 处理专利文档文件选择
    const handlePatentDocumentChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (file.size > 10 * 1024 * 1024) { // 10MB
          alert('文件大小不能超过 10MB')
          event.target.value = ''
          return
        }
        formData.patentDocument = file
      }
    }

    // 处理证明文档文件选择
    const handleProofDocumentChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (file.size > 10 * 1024 * 1024) { // 10MB
          alert('文件大小不能超过 10MB')
          event.target.value = ''
          return
        }
        formData.proofDocument = file
      }
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
        patentName: '',
        patentNumber: '',
        category: '',
        price: '',
        applicationDate: '',
        expirationDate: '',
        summary: '',
        ownerName: '',
        ownerIdCard: '',
        isAgency: false,
        patentDocument: null,
        proofDocument: null,
      })

      // 清空文件输入框
      const patentDocInput = document.getElementById('patentDocument')
      const proofDocInput = document.getElementById('proofDocument')
      if (patentDocInput) patentDocInput.value = ''
      if (proofDocInput) proofDocInput.value = ''
    }

    // 更新上传进度
    const updateUploadProgress = (message, progress, error = '') => {
      uploadStatus.message = message
      uploadStatus.progress = progress
      uploadStatus.error = error
    }

    // 提交专利
    const submitPatent = async () => {
      try {
        isSubmitting.value = true

        // 验证表单
        if (!validateForm()) {
          return
        }

        // 显示上传进度模态框
        if (uploadProgressModal) {
          uploadProgressModal.show()
        }

        // 重置上传状态
        uploadStatus.completed = false
        uploadStatus.error = ''

        // 步骤1: 上传专利文档到 IPFS
        updateUploadProgress('正在上传专利文档...', 20)
        const documentHash = await patentService.uploadToIPFS(formData.patentDocument)

        // 步骤2: 上传证明文档到 IPFS (如果是代理销售)
        let proofDocumentHash = ''
        if (formData.isAgency && formData.proofDocument) {
          updateUploadProgress('正在上传证明文档...', 40)
          proofDocumentHash = await patentService.uploadToIPFS(formData.proofDocument)
        }

        // 步骤3: 创建专利记录
        updateUploadProgress('正在创建专利记录...', 60)
        const patentData = {
          patentName: formData.patentName,
          patentNumber: formData.patentNumber,
          category: formData.category,
          price: parseFloat(formData.price),
          summary: formData.summary,
          applicationDate: formData.applicationDate,
          expirationDate: formData.expirationDate,
          ownerName: formData.ownerName,
          ownerIdCard: formData.ownerIdCard,
          isAgency: formData.isAgency,
          documentHash,
          proofDocumentHash,
          uploaderAddress: metaMaskStore.currentAccount,
        }

        const result = await patentService.createPatent(patentData)

        // 步骤4: 完成
        updateUploadProgress('专利上传成功！', 100)
        uploadStatus.completed = true

        console.log('专利创建成功:', result)

      } catch (error) {
        console.error('专利上传失败:', error)
        updateUploadProgress('上传失败', uploadStatus.progress, error.message)
      } finally {
        isSubmitting.value = false
      }
    }

    // 验证表单
    const validateForm = () => {
      if (!formData.patentName.trim()) {
        alert('请输入专利名称')
        return false
      }

      if (!formData.patentNumber.trim()) {
        alert('请输入专利号')
        return false
      }

      if (!formData.category) {
        alert('请选择专利分类')
        return false
      }

      if (!formData.price || parseFloat(formData.price) <= 0) {
        alert('请输入有效的价格')
        return false
      }

      if (!formData.applicationDate) {
        alert('请选择申请日期')
        return false
      }

      if (!formData.expirationDate) {
        alert('请选择到期日期')
        return false
      }

      if (new Date(formData.expirationDate) <= new Date(formData.applicationDate)) {
        alert('到期日期必须晚于申请日期')
        return false
      }

      if (!formData.summary.trim()) {
        alert('请输入专利摘要')
        return false
      }

      if (!formData.ownerName.trim()) {
        alert('请输入权利人姓名')
        return false
      }

      if (!formData.ownerIdCard.trim()) {
        alert('请输入身份证号')
        return false
      }

      if (!formData.patentDocument) {
        alert('请上传专利文档')
        return false
      }

      if (formData.isAgency && !formData.proofDocument) {
        alert('代理销售需要上传证明文档')
        return false
      }

      return true
    }

    // 处理上传完成
    const handleUploadComplete = () => {
      if (uploadStatus.completed && !uploadStatus.error) {
        // 跳转到我的专利页面
        router.push('/my-patents/published')
      }
    }

    return {
      formData,
      categories,
      isSubmitting,
      uploadStatus,
      handlePatentDocumentChange,
      handleProofDocumentChange,
      resetForm,
      submitPatent,
      handleUploadComplete,
    }
  },
}
</script>

<style scoped>
.patent-upload {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h4 {
  color: #495057;
  font-weight: 600;
}

.text-primary {
  color: #0d6efd !important;
}

.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control,
.form-select {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  padding: 0.75rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
}

.progress {
  height: 1rem;
  border-radius: 0.5rem;
}

.progress-bar {
  background-color: #0d6efd;
  border-radius: 0.5rem;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c2c7;
  color: #842029;
  border-radius: 0.375rem;
}

.modal-content {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 0.5rem 0.5rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

hr {
  margin: 1rem 0;
  color: #dee2e6;
  border: 0;
  border-top: 1px solid;
  opacity: 0.25;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #6c757d;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .card {
    margin: 1rem 0;
  }

  .d-grid.gap-2.d-md-flex {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}
</style>
