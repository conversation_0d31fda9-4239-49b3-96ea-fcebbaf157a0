<template>
  <div class="my-patents">
    <div class="container mt-4">
      <!-- 页面标题和导航 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <h3>我的专利</h3>
            <button
              class="btn btn-primary"
              @click="$router.push('/patent/upload')"
            >
              <i class="bi bi-plus-circle"></i>
              上传专利
            </button>
          </div>
        </div>
      </div>

      <!-- 标签页导航 -->
      <div class="row mb-4">
        <div class="col-12">
          <ul class="nav nav-tabs" id="patentTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                :class="{ active: activeTab === 'published' }"
                @click="switchTab('published')"
                type="button"
              >
                <i class="bi bi-upload"></i>
                我发布的专利
                <span v-if="publishedCount > 0" class="badge bg-primary ms-2">
                  {{ publishedCount }}
                </span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                :class="{ active: activeTab === 'purchased' }"
                @click="switchTab('purchased')"
                type="button"
              >
                <i class="bi bi-cart-check"></i>
                我购买的专利
                <span v-if="purchasedCount > 0" class="badge bg-success ms-2">
                  {{ purchasedCount }}
                </span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                :class="{ active: activeTab === 'owned' }"
                @click="switchTab('owned')"
                type="button"
              >
                <i class="bi bi-award"></i>
                我拥有的专利
                <span v-if="ownedCount > 0" class="badge bg-warning ms-2">
                  {{ ownedCount }}
                </span>
              </button>
            </li>
          </ul>
        </div>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content" id="patentTabsContent">
        <!-- 我发布的专利 -->
        <div
          v-show="activeTab === 'published'"
          class="tab-pane fade"
          :class="{ 'show active': activeTab === 'published' }"
        >
          <div class="card">
            <div class="card-header">
              <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">我发布的专利</h5>
                <div class="d-flex align-items-center gap-2">
                  <select
                    class="form-select form-select-sm"
                    v-model="publishedFilter"
                    @change="loadPublishedPatents"
                    style="width: auto;"
                  >
                    <option value="">全部状态</option>
                    <option value="PENDING">待审核</option>
                    <option value="APPROVED">已上架</option>
                    <option value="REJECTED">已拒绝</option>
                    <option value="TRADING">交易中</option>
                    <option value="SOLD">已售出</option>
                    <option value="FROZEN">已冻结</option>
                  </select>
                  <button
                    class="btn btn-outline-primary btn-sm"
                    @click="loadPublishedPatents"
                    :disabled="isLoading"
                  >
                    <i class="bi bi-arrow-clockwise"></i>
                    刷新
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body">
              <PublishedPatentsList
                :patents="publishedPatents"
                :loading="isLoading"
                @status-change="handleStatusChange"
                @revoke="handleRevoke"
                @relist="handleRelist"
              />
            </div>
          </div>
        </div>

        <!-- 我购买的专利 -->
        <div
          v-show="activeTab === 'purchased'"
          class="tab-pane fade"
          :class="{ 'show active': activeTab === 'purchased' }"
        >
          <div class="card">
            <div class="card-header">
              <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">我购买的专利</h5>
                <button
                  class="btn btn-outline-primary btn-sm"
                  @click="loadPurchasedPatents"
                  :disabled="isLoading"
                >
                  <i class="bi bi-arrow-clockwise"></i>
                  刷新
                </button>
              </div>
            </div>
            <div class="card-body">
              <PurchasedPatentsList
                :patents="purchasedPatents"
                :loading="isLoading"
                @view-contract="handleViewContract"
                @download-contract="handleDownloadContract"
              />
            </div>
          </div>
        </div>

        <!-- 我拥有的专利 -->
        <div
          v-show="activeTab === 'owned'"
          class="tab-pane fade"
          :class="{ 'show active': activeTab === 'owned' }"
        >
          <div class="card">
            <div class="card-header">
              <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">我拥有的专利</h5>
                <button
                  class="btn btn-outline-primary btn-sm"
                  @click="loadOwnedPatents"
                  :disabled="isLoading"
                >
                  <i class="bi bi-arrow-clockwise"></i>
                  刷新
                </button>
              </div>
            </div>
            <div class="card-body">
              <OwnedPatentsList
                :patents="ownedPatents"
                :loading="isLoading"
                @relist="handleRelistOwned"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交易合同模态框 -->
    <TradeContractModal
      :contract="selectedContract"
      @download="handleContractDownload"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMetaMaskStore } from '@/stores/metamask'
import patentService from '@/services/patentService'
import PublishedPatentsList from '@/components/PublishedPatentsList.vue'
import PurchasedPatentsList from '@/components/PurchasedPatentsList.vue'
import OwnedPatentsList from '@/components/OwnedPatentsList.vue'
import TradeContractModal from '@/components/TradeContractModal.vue'

export default {
  name: 'MyPatents',
  components: {
    PublishedPatentsList,
    PurchasedPatentsList,
    OwnedPatentsList,
    TradeContractModal,
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const metaMaskStore = useMetaMaskStore()

    // 响应式数据
    const activeTab = ref('published')
    const isLoading = ref(false)
    const publishedFilter = ref('')

    const publishedPatents = ref([])
    const purchasedPatents = ref([])
    const ownedPatents = ref([])

    const selectedContract = ref(null)

    // 计算属性
    const publishedCount = computed(() => publishedPatents.value.length)
    const purchasedCount = computed(() => purchasedPatents.value.length)
    const ownedCount = computed(() => ownedPatents.value.length)

    // 监听路由变化
    watch(() => route.params.type, (newType) => {
      if (newType && ['published', 'purchased', 'owned'].includes(newType)) {
        activeTab.value = newType
      }
    }, { immediate: true })

    // 组件挂载时初始化
    onMounted(() => {
      // 检查 MetaMask 连接状态
      if (!metaMaskStore.isConnected) {
        router.push('/')
        return
      }

      // 根据路由参数设置活动标签
      const tabType = route.params.type || 'published'
      if (['published', 'purchased', 'owned'].includes(tabType)) {
        activeTab.value = tabType
      }

      // 加载数据
      loadCurrentTabData()
    })

    // 切换标签页
    const switchTab = (tab) => {
      activeTab.value = tab

      // 更新路由
      const newPath = `/my-patents/${tab}`
      if (route.path !== newPath) {
        router.push(newPath)
      }

      // 加载对应数据
      loadCurrentTabData()
    }

    // 加载当前标签页数据
    const loadCurrentTabData = () => {
      switch (activeTab.value) {
        case 'published':
          loadPublishedPatents()
          break
        case 'purchased':
          loadPurchasedPatents()
          break
        case 'owned':
          loadOwnedPatents()
          break
      }
    }

    // 加载我发布的专利
    const loadPublishedPatents = async () => {
      try {
        isLoading.value = true
        const result = await patentService.getMyPublishedPatents(metaMaskStore.currentAccount)

        let patents = result.patents || []

        // 根据状态过滤
        if (publishedFilter.value) {
          patents = patents.filter(patent => patent.status === publishedFilter.value)
        }

        publishedPatents.value = patents
      } catch (error) {
        console.error('加载我发布的专利失败:', error)
        alert('加载失败，请稍后重试')
      } finally {
        isLoading.value = false
      }
    }

    // 加载我购买的专利
    const loadPurchasedPatents = async () => {
      try {
        isLoading.value = true
        const result = await patentService.getMyPurchasedPatents(metaMaskStore.currentAccount)
        purchasedPatents.value = result.patents || []
      } catch (error) {
        console.error('加载我购买的专利失败:', error)
        alert('加载失败，请稍后重试')
      } finally {
        isLoading.value = false
      }
    }

    // 加载我拥有的专利
    const loadOwnedPatents = async () => {
      try {
        isLoading.value = true
        const result = await patentService.getMyOwnedPatents(metaMaskStore.currentAccount)
        ownedPatents.value = result.patents || []
      } catch (error) {
        console.error('加载我拥有的专利失败:', error)
        alert('加载失败，请稍后重试')
      } finally {
        isLoading.value = false
      }
    }

    // 处理状态变更
    const handleStatusChange = async (patentId, newStatus) => {
      try {
        await patentService.updatePatentStatus(patentId, newStatus, metaMaskStore.currentAccount)
        alert('状态更新成功')
        loadPublishedPatents()
      } catch (error) {
        console.error('更新状态失败:', error)
        alert('更新失败，请稍后重试')
      }
    }

    // 处理撤销专利
    const handleRevoke = async (patentId) => {
      if (!confirm('确定要撤销这个专利吗？撤销后无法恢复。')) {
        return
      }

      try {
        await patentService.updatePatentStatus(patentId, 'REVOKED', metaMaskStore.currentAccount)
        alert('专利撤销成功')
        loadPublishedPatents()
      } catch (error) {
        console.error('撤销专利失败:', error)
        alert('撤销失败，请稍后重试')
      }
    }

    // 处理重新上架
    const handleRelist = async (patentId, newPrice) => {
      try {
        await patentService.relistPatent(patentId, newPrice, metaMaskStore.currentAccount)
        alert('重新上架成功')
        loadPublishedPatents()
      } catch (error) {
        console.error('重新上架失败:', error)
        alert('重新上架失败，请稍后重试')
      }
    }

    // 处理拥有专利的重新上架
    const handleRelistOwned = async (patentId, newPrice) => {
      try {
        await patentService.relistPatent(patentId, newPrice, metaMaskStore.currentAccount)
        alert('重新上架成功')
        loadOwnedPatents()
      } catch (error) {
        console.error('重新上架失败:', error)
        alert('重新上架失败，请稍后重试')
      }
    }

    // 查看合同
    const handleViewContract = (contract) => {
      selectedContract.value = contract
    }

    // 下载合同
    const handleDownloadContract = (contractId) => {
      console.log('下载合同:', contractId)
      // 实际下载逻辑
    }

    // 处理合同下载
    const handleContractDownload = (contract) => {
      console.log('下载合同文件:', contract)
      // 实际下载逻辑
    }

    return {
      activeTab,
      isLoading,
      publishedFilter,
      publishedPatents,
      purchasedPatents,
      ownedPatents,
      selectedContract,
      publishedCount,
      purchasedCount,
      ownedCount,
      switchTab,
      loadPublishedPatents,
      loadPurchasedPatents,
      loadOwnedPatents,
      handleStatusChange,
      handleRevoke,
      handleRelist,
      handleRelistOwned,
      handleViewContract,
      handleDownloadContract,
      handleContractDownload,
    }
  },
}
</script>

<style scoped>
.my-patents {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h5 {
  color: #495057;
  font-weight: 600;
}

.nav-tabs {
  border-bottom: 2px solid #dee2e6;
}

.nav-tabs .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
  color: #6c757d;
  font-weight: 500;
  padding: 1rem 1.5rem;
  transition: all 0.15s ease-in-out;
}

.nav-tabs .nav-link:hover {
  border-color: transparent;
  color: #0d6efd;
  background-color: transparent;
}

.nav-tabs .nav-link.active {
  color: #0d6efd;
  background-color: transparent;
  border-color: transparent transparent #0d6efd transparent;
}

.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
  font-weight: 500;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.btn-outline-primary {
  color: #0d6efd;
  border-color: #0d6efd;
  font-weight: 500;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.form-select-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.tab-content {
  margin-top: 0;
}

.tab-pane {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .nav-tabs .nav-link i {
    display: none;
  }

  .card-header .d-flex {
    flex-direction: column;
    gap: 1rem;
  }

  .btn {
    width: 100%;
  }

  .form-select-sm {
    width: 100% !important;
  }
}

@media (max-width: 576px) {
  .nav-tabs {
    flex-direction: column;
  }

  .nav-tabs .nav-link {
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0;
  }

  .nav-tabs .nav-link.active {
    border-color: #dee2e6;
    background-color: #e9ecef;
  }
}
</style>
