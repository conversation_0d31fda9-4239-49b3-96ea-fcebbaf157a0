<template>
  <div class="patent-detail">
    <div class="container mt-4">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载专利详情...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="alert alert-danger" role="alert">
        <h4 class="alert-heading">加载失败</h4>
        <p>{{ error }}</p>
        <hr>
        <div class="d-flex justify-content-between">
          <button class="btn btn-outline-danger" @click="$router.go(-1)">
            返回上一页
          </button>
          <button class="btn btn-danger" @click="loadPatentDetail">
            重新加载
          </button>
        </div>
      </div>

      <!-- 专利详情内容 -->
      <div v-else-if="patent" class="row">
        <!-- 左侧主要信息 -->
        <div class="col-lg-8">
          <!-- 专利基本信息 -->
          <div class="card mb-4">
            <div class="card-header">
              <div class="d-flex justify-content-between align-items-start">
                <div>
                  <h4 class="mb-1">{{ patent.patentName }}</h4>
                  <p class="text-muted mb-0">专利号: {{ patent.patentNumber }}</p>
                </div>
                <span class="badge fs-6" :class="getStatusBadgeClass(patent.status)">
                  {{ getStatusText(patent.status) }}
                </span>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <strong>专利分类:</strong>
                  <span class="ms-2">{{ getCategoryText(patent.category) }}</span>
                </div>
                <div class="col-md-6 mb-3">
                  <strong>价格:</strong>
                  <span class="ms-2 text-success fw-bold">{{ patent.price }} ETH</span>
                </div>
                <div class="col-md-6 mb-3">
                  <strong>申请日期:</strong>
                  <span class="ms-2">{{ formatDate(patent.applicationDate) }}</span>
                </div>
                <div class="col-md-6 mb-3">
                  <strong>到期日期:</strong>
                  <span class="ms-2">{{ formatDate(patent.expirationDate) }}</span>
                </div>
                <div class="col-md-6 mb-3">
                  <strong>权利人:</strong>
                  <span class="ms-2">{{ patent.ownerName }}</span>
                </div>
                <div class="col-md-6 mb-3">
                  <strong>上传时间:</strong>
                  <span class="ms-2">{{ formatDate(patent.createdAt) }}</span>
                </div>
                <div v-if="patent.isAgency" class="col-12 mb-3">
                  <span class="badge bg-warning text-dark">
                    <i class="bi bi-building"></i>
                    代理销售
                  </span>
                </div>
              </div>

              <div class="mt-4">
                <h6>专利摘要</h6>
                <p class="text-muted">{{ patent.summary }}</p>
              </div>
            </div>
          </div>

          <!-- 上传者信息 -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-person-circle"></i>
                上传者信息
              </h5>
            </div>
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="avatar-placeholder me-3">
                  <i class="bi bi-person-fill"></i>
                </div>
                <div>
                  <h6 class="mb-1">{{ uploaderInfo.name || '匿名用户' }}</h6>
                  <p class="text-muted mb-1">
                    地址: {{ formatAddress(patent.uploaderAddress) }}
                  </p>
                  <p class="text-muted mb-0">
                    注册时间: {{ formatDate(uploaderInfo.registeredAt) }}
                  </p>
                </div>
                <div class="ms-auto">
                  <button
                    class="btn btn-outline-primary btn-sm"
                    @click="viewUploaderProfile"
                  >
                    查看资料
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 专利文档 -->
          <PatentDocuments
            :patent="patent"
            @download="handleDocumentDownload"
          />

          <!-- 专利事件历史 -->
          <PatentEvents
            :patent-id="patent.id"
            @events-loaded="handleEventsLoaded"
          />
        </div>

        <!-- 右侧操作面板 -->
        <div class="col-lg-4">
          <div class="sticky-top" style="top: 2rem;">
            <!-- 交易按钮 -->
            <TradeButton
              v-if="canTrade"
              :patent="patent"
              @trade-initiated="handleTradeInitiated"
            />

            <!-- 专利统计信息 -->
            <div class="card mt-4">
              <div class="card-header">
                <h6 class="mb-0">
                  <i class="bi bi-graph-up"></i>
                  专利统计
                </h6>
              </div>
              <div class="card-body">
                <div class="stat-item">
                  <span class="stat-label">浏览次数:</span>
                  <span class="stat-value">{{ patent.viewCount || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">收藏次数:</span>
                  <span class="stat-value">{{ patent.favoriteCount || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">交易次数:</span>
                  <span class="stat-value">{{ patent.tradeCount || 0 }}</span>
                </div>
              </div>
            </div>

            <!-- 相关专利推荐 -->
            <div class="card mt-4">
              <div class="card-header">
                <h6 class="mb-0">
                  <i class="bi bi-lightbulb"></i>
                  相关专利
                </h6>
              </div>
              <div class="card-body">
                <div v-if="relatedPatents.length === 0" class="text-muted text-center py-3">
                  暂无相关专利
                </div>
                <div v-else>
                  <div
                    v-for="relatedPatent in relatedPatents"
                    :key="relatedPatent.id"
                    class="related-patent-item"
                    @click="viewRelatedPatent(relatedPatent.id)"
                  >
                    <h6 class="mb-1">{{ truncateText(relatedPatent.patentName, 40) }}</h6>
                    <p class="text-muted mb-1">{{ relatedPatent.price }} ETH</p>
                    <small class="text-muted">{{ getCategoryText(relatedPatent.category) }}</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMetaMaskStore } from '@/stores/metamask'
import patentService from '@/services/patentService'
import PatentDocuments from '@/components/PatentDocuments.vue'
import PatentEvents from '@/components/PatentEvents.vue'
import TradeButton from '@/components/TradeButton.vue'

export default {
  name: 'PatentDetail',
  components: {
    PatentDocuments,
    PatentEvents,
    TradeButton,
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const metaMaskStore = useMetaMaskStore()

    // 响应式数据
    const isLoading = ref(true)
    const error = ref('')
    const patent = ref(null)
    const uploaderInfo = reactive({
      name: '',
      registeredAt: '',
    })
    const relatedPatents = ref([])

    // 计算属性
    const canTrade = computed(() => {
      if (!patent.value || !metaMaskStore.isConnected) return false

      // 不能交易自己上传的专利
      if (patent.value.uploaderAddress === metaMaskStore.currentAccount) return false

      // 只有已上架的专利可以交易
      return patent.value.status === 'APPROVED'
    })

    // 组件挂载时加载数据
    onMounted(() => {
      loadPatentDetail()
    })

    // 加载专利详情
    const loadPatentDetail = async () => {
      try {
        isLoading.value = true
        error.value = ''

        const patentId = route.params.id
        if (!patentId) {
          throw new Error('专利ID不能为空')
        }

        // 获取专利详情
        const result = await patentService.getPatentDetail(patentId)
        patent.value = result.patent

        // 获取上传者信息
        if (result.patent.uploaderAddress) {
          try {
            // 这里应该调用用户服务获取上传者信息
            // const userInfo = await userService.getUserInfo(result.patent.uploaderAddress)
            // uploaderInfo.name = userInfo.name
            // uploaderInfo.registeredAt = userInfo.registeredAt

            // 临时使用默认值
            uploaderInfo.name = '用户' + result.patent.uploaderAddress.slice(-4)
            uploaderInfo.registeredAt = result.patent.createdAt
          } catch (err) {
            console.warn('获取上传者信息失败:', err)
          }
        }

        // 获取相关专利推荐
        loadRelatedPatents()

      } catch (err) {
        console.error('加载专利详情失败:', err)
        error.value = err.message || '加载失败，请稍后重试'
      } finally {
        isLoading.value = false
      }
    }

    // 加载相关专利
    const loadRelatedPatents = async () => {
      try {
        if (!patent.value) return

        const result = await patentService.searchPatents({
          category: patent.value.category,
          limit: 5,
        })

        // 过滤掉当前专利
        relatedPatents.value = (result.patents || []).filter(
          p => p.id !== patent.value.id
        ).slice(0, 3)

      } catch (err) {
        console.warn('加载相关专利失败:', err)
      }
    }

    // 工具方法
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    const formatAddress = (address) => {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    const getCategoryText = (category) => {
      const categoryMap = {
        invention: '发明专利',
        utility: '实用新型专利',
        design: '外观设计专利',
        software: '软件著作权',
        trademark: '商标',
      }
      return categoryMap[category] || category
    }

    const getStatusText = (status) => {
      const statusMap = {
        PENDING: '待审核',
        APPROVED: '已上架',
        REJECTED: '已拒绝',
        TRADING: '交易中',
        SOLD: '已售出',
        FROZEN: '已冻结',
        REVOKED: '已撤销',
      }
      return statusMap[status] || status
    }

    const getStatusBadgeClass = (status) => {
      const classMap = {
        PENDING: 'bg-warning',
        APPROVED: 'bg-success',
        REJECTED: 'bg-danger',
        TRADING: 'bg-info',
        SOLD: 'bg-secondary',
        FROZEN: 'bg-dark',
        REVOKED: 'bg-danger',
      }
      return classMap[status] || 'bg-secondary'
    }

    const truncateText = (text, maxLength) => {
      if (!text) return ''
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    }

    // 事件处理
    const handleDocumentDownload = (documentHash) => {
      console.log('下载文档:', documentHash)
      // 实际下载逻辑在 PatentDocuments 组件中处理
    }

    const handleEventsLoaded = (events) => {
      console.log('专利事件加载完成:', events)
    }

    const handleTradeInitiated = (tradeData) => {
      console.log('交易发起:', tradeData)
      // 跳转到交易页面
      router.push(`/patent/${patent.value.id}/trade`)
    }

    const viewUploaderProfile = () => {
      // 跳转到上传者资料页面
      console.log('查看上传者资料:', patent.value.uploaderAddress)
    }

    const viewRelatedPatent = (patentId) => {
      router.push(`/patent/${patentId}`)
    }

    return {
      isLoading,
      error,
      patent,
      uploaderInfo,
      relatedPatents,
      canTrade,
      loadPatentDetail,
      formatDate,
      formatAddress,
      getCategoryText,
      getStatusText,
      getStatusBadgeClass,
      truncateText,
      handleDocumentDownload,
      handleEventsLoaded,
      handleTradeInitiated,
      viewUploaderProfile,
      viewRelatedPatent,
    }
  },
}
</script>

<style scoped>
.patent-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h4,
.card-header h5,
.card-header h6 {
  color: #495057;
  font-weight: 600;
}

.badge {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
}

.avatar-placeholder {
  width: 3rem;
  height: 3rem;
  background-color: #e9ecef;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 1.5rem;
}

.stat-item {
  display: flex;
  justify-content: between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #6c757d;
  font-size: 0.875rem;
}

.stat-value {
  font-weight: 600;
  color: #495057;
  margin-left: auto;
}

.related-patent-item {
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.related-patent-item:hover {
  background-color: #f8f9fa;
  border-color: #0d6efd;
  transform: translateY(-1px);
}

.related-patent-item:last-child {
  margin-bottom: 0;
}

.related-patent-item h6 {
  color: #495057;
  font-size: 0.875rem;
  font-weight: 600;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.alert-heading {
  color: inherit;
}

.btn-outline-primary {
  color: #0d6efd;
  border-color: #0d6efd;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.text-success {
  color: #198754 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.sticky-top {
  position: sticky;
  top: 2rem;
  z-index: 1020;
}

@media (max-width: 992px) {
  .sticky-top {
    position: static;
  }

  .col-lg-4 {
    margin-top: 2rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .card {
    margin: 1rem 0;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .badge {
    align-self: flex-start;
    margin-top: 0.5rem;
  }

  .avatar-placeholder {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }

  .related-patent-item {
    padding: 0.5rem;
  }

  .related-patent-item h6 {
    font-size: 0.8rem;
  }
}
</style>
