<template>
  <div class="patent-search">
    <div class="container mt-4">
      <!-- 搜索表单 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h4 class="mb-0">
                <i class="bi bi-search"></i>
                专利搜索
              </h4>
            </div>
            <div class="card-body">
              <form @submit.prevent="searchPatents">
                <div class="row">
                  <div class="col-md-4 mb-3">
                    <label for="searchType" class="form-label">搜索类型</label>
                    <select
                      class="form-select"
                      id="searchType"
                      v-model="searchParams.searchType"
                    >
                      <option value="fuzzy">模糊搜索</option>
                      <option value="exact">精确搜索</option>
                      <option value="number">专利号搜索</option>
                    </select>
                  </div>
                  <div class="col-md-4 mb-3">
                    <label for="keyword" class="form-label">
                      {{ searchParams.searchType === 'number' ? '专利号' : '关键词' }}
                    </label>
                    <input
                      type="text"
                      class="form-control"
                      id="keyword"
                      v-model="searchParams.keyword"
                      :placeholder="searchParams.searchType === 'number' ? '请输入专利号' : '请输入搜索关键词'"
                    >
                  </div>
                  <div class="col-md-4 mb-3">
                    <label for="category" class="form-label">专利分类</label>
                    <select
                      class="form-select"
                      id="category"
                      v-model="searchParams.category"
                    >
                      <option value="">全部分类</option>
                      <option
                        v-for="category in categories"
                        :key="category.value"
                        :value="category.value"
                      >
                        {{ category.label }}
                      </option>
                    </select>
                  </div>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                  <button
                    type="button"
                    class="btn btn-outline-secondary"
                    @click="resetSearch"
                  >
                    重置
                  </button>
                  <button
                    type="submit"
                    class="btn btn-primary"
                    :disabled="isSearching"
                  >
                    <span
                      v-if="isSearching"
                      class="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    {{ isSearching ? '搜索中...' : '搜索' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                搜索结果
                <span v-if="searchResults.total > 0" class="text-muted">
                  (共 {{ searchResults.total }} 条)
                </span>
              </h5>
              <div v-if="searchResults.total > 0" class="d-flex align-items-center">
                <label for="pageSize" class="form-label me-2 mb-0">每页显示:</label>
                <select
                  class="form-select form-select-sm"
                  id="pageSize"
                  v-model="searchParams.limit"
                  @change="searchPatents"
                  style="width: auto;"
                >
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
              </div>
            </div>
            <div class="card-body">
              <!-- 加载状态 -->
              <div v-if="isSearching" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">搜索中...</span>
                </div>
                <p class="mt-2 text-muted">正在搜索专利...</p>
              </div>

              <!-- 无结果 -->
              <div v-else-if="searchResults.patents.length === 0 && hasSearched" class="text-center py-5">
                <i class="bi bi-search display-1 text-muted"></i>
                <h5 class="mt-3 text-muted">未找到相关专利</h5>
                <p class="text-muted">请尝试调整搜索条件</p>
              </div>

              <!-- 初始状态 -->
              <div v-else-if="!hasSearched" class="text-center py-5">
                <i class="bi bi-search display-1 text-muted"></i>
                <h5 class="mt-3 text-muted">请输入搜索条件</h5>
                <p class="text-muted">支持专利名称、专利号等多种搜索方式</p>
              </div>

              <!-- 搜索结果列表 -->
              <div v-else>
                <PatentList
                  :patents="searchResults.patents"
                  @view-detail="handleViewDetail"
                />

                <!-- 分页 -->
                <nav v-if="searchResults.totalPages > 1" aria-label="搜索结果分页" class="mt-4">
                  <ul class="pagination justify-content-center">
                    <li class="page-item" :class="{ disabled: searchParams.page <= 1 }">
                      <button
                        class="page-link"
                        @click="changePage(searchParams.page - 1)"
                        :disabled="searchParams.page <= 1"
                      >
                        上一页
                      </button>
                    </li>

                    <li
                      v-for="page in visiblePages"
                      :key="page"
                      class="page-item"
                      :class="{ active: page === searchParams.page }"
                    >
                      <button
                        class="page-link"
                        @click="changePage(page)"
                      >
                        {{ page }}
                      </button>
                    </li>

                    <li class="page-item" :class="{ disabled: searchParams.page >= searchResults.totalPages }">
                      <button
                        class="page-link"
                        @click="changePage(searchParams.page + 1)"
                        :disabled="searchParams.page >= searchResults.totalPages"
                      >
                        下一页
                      </button>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import patentService from '@/services/patentService'
import PatentList from '@/components/PatentList.vue'

export default {
  name: 'PatentSearch',
  components: {
    PatentList,
  },
  setup() {
    const router = useRouter()

    // 响应式数据
    const isSearching = ref(false)
    const hasSearched = ref(false)
    const categories = ref([
      { value: 'invention', label: '发明专利' },
      { value: 'utility', label: '实用新型专利' },
      { value: 'design', label: '外观设计专利' },
      { value: 'software', label: '软件著作权' },
      { value: 'trademark', label: '商标' },
    ])

    const searchParams = reactive({
      searchType: 'fuzzy',
      keyword: '',
      category: '',
      page: 1,
      limit: 20,
    })

    const searchResults = reactive({
      patents: [],
      total: 0,
      totalPages: 0,
      currentPage: 1,
    })

    // 计算可见的页码
    const visiblePages = computed(() => {
      const pages = []
      const total = searchResults.totalPages
      const current = searchParams.page

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        }
      }

      return pages.filter(page => page !== '...' || pages.indexOf(page) === pages.lastIndexOf(page))
    })

    // 搜索专利
    const searchPatents = async () => {
      try {
        isSearching.value = true
        hasSearched.value = true

        const params = {
          ...searchParams,
        }

        // 根据搜索类型设置参数
        if (searchParams.searchType === 'number') {
          params.patentNumber = searchParams.keyword
          delete params.keyword
        }

        const result = await patentService.searchPatents(params)

        searchResults.patents = result.patents || []
        searchResults.total = result.total || 0
        searchResults.totalPages = result.totalPages || 0
        searchResults.currentPage = result.currentPage || 1

      } catch (error) {
        console.error('搜索专利失败:', error)
        alert('搜索失败，请稍后重试')
      } finally {
        isSearching.value = false
      }
    }

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchParams, {
        searchType: 'fuzzy',
        keyword: '',
        category: '',
        page: 1,
        limit: 20,
      })

      Object.assign(searchResults, {
        patents: [],
        total: 0,
        totalPages: 0,
        currentPage: 1,
      })

      hasSearched.value = false
    }

    // 切换页码
    const changePage = (page) => {
      if (page >= 1 && page <= searchResults.totalPages && page !== searchParams.page) {
        searchParams.page = page
        searchPatents()
      }
    }

    // 查看专利详情
    const handleViewDetail = (patent) => {
      router.push(`/patent/${patent.id}`)
    }

    return {
      isSearching,
      hasSearched,
      categories,
      searchParams,
      searchResults,
      visiblePages,
      searchPatents,
      resetSearch,
      changePage,
      handleViewDetail,
    }
  },
}
</script>

<style scoped>
.patent-search {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h4,
.card-header h5 {
  color: #495057;
  font-weight: 600;
}

.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control,
.form-select {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  padding: 0.75rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.text-muted {
  color: #6c757d !important;
}

.display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}

.pagination {
  margin-bottom: 0;
}

.page-link {
  color: #0d6efd;
  background-color: #fff;
  border: 1px solid #dee2e6;
  padding: 0.75rem 1rem;
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.page-link:hover {
  color: #0a58ca;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.page-item.active .page-link {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .card {
    margin: 1rem 0;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .btn {
    width: 100%;
  }

  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}
</style>
