/**
 * 用户资料管理 Composable
 */

import { ref, reactive, computed } from 'vue'
import { useMetaMaskStore } from '@/stores/metamask'
import userService from '@/services/userService'

export function useUserProfile() {
  const metamaskStore = useMetaMaskStore()
  
  const loading = ref(false)
  const saving = ref(false)
  const isFirstTimeUser = ref(false)
  
  const profileForm = reactive({
    realName: '',
    phone: '',
    idCard: '',
    email: ''
  })
  
  const errors = reactive({
    realName: '',
    phone: '',
    idCard: '',
    email: ''
  })

  const needsBlockchainConfirmation = computed(() => {
    return profileForm.realName || profileForm.phone || profileForm.idCard
  })

  // 验证表单
  const validateForm = () => {
    clearErrors()
    let isValid = true
    
    if (!profileForm.realName.trim()) {
      errors.realName = '请输入真实姓名'
      isValid = false
    }
    
    if (!profileForm.phone.trim()) {
      errors.phone = '请输入手机号码'
      isValid = false
    } else if (!/^1[3-9]\d{9}$/.test(profileForm.phone)) {
      errors.phone = '请输入有效的手机号码'
      isValid = false
    }
    
    if (!profileForm.idCard.trim()) {
      errors.idCard = '请输入身份证号码'
      isValid = false
    } else if (!/^\d{15}$|^\d{17}[\dxX]$/.test(profileForm.idCard)) {
      errors.idCard = '请输入有效的身份证号码'
      isValid = false
    }
    
    if (profileForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileForm.email)) {
      errors.email = '请输入有效的邮箱地址'
      isValid = false
    }
    
    return isValid
  }

  const clearErrors = () => {
    Object.keys(errors).forEach(key => {
      errors[key] = ''
    })
  }

  // 加载用户资料
  const loadProfile = async () => {
    if (!metamaskStore.currentAccount) return
    
    loading.value = true
    
    try {
      // 尝试从 API 获取用户资料
      const profileData = await userService.getUserProfile(metamaskStore.currentAccount)
      Object.assign(profileForm, profileData)
      
      // 检查是否为首次使用用户
      isFirstTimeUser.value = await userService.isFirstTimeUser(metamaskStore.currentAccount)
    } catch (error) {
      console.error('加载用户资料失败:', error)
      
      // 如果 API 调用失败，使用模拟数据
      const mockData = {
        realName: '',
        phone: '',
        idCard: '',
        email: ''
      }
      
      Object.assign(profileForm, mockData)
      isFirstTimeUser.value = true
    } finally {
      loading.value = false
    }
  }

  // 保存资料
  const saveProfile = async () => {
    if (!validateForm()) return false
    
    saving.value = true
    
    try {
      // 模拟区块链交易确认
      if (needsBlockchainConfirmation.value) {
        // TODO: 调用智能合约更新信息
        await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟等待
      }
      
      // 尝试调用 API 保存用户资料
      try {
        await userService.updateUserProfile(metamaskStore.currentAccount, {
          ...profileForm
        })
      } catch (apiError) {
        console.warn('API 保存失败，使用本地模拟:', apiError)
        // 如果 API 调用失败，模拟保存成功
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
      isFirstTimeUser.value = false
      return true
    } catch (error) {
      console.error('保存失败:', error)
      throw error
    } finally {
      saving.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(profileForm, {
      realName: '',
      phone: '',
      idCard: '',
      email: ''
    })
    clearErrors()
  }

  // 复制地址到剪贴板
  const copyAddress = async () => {
    try {
      await navigator.clipboard.writeText(metamaskStore.currentAccount)
      return true
    } catch (err) {
      console.error('复制失败:', err)
      return false
    }
  }

  return {
    // 状态
    loading,
    saving,
    isFirstTimeUser,
    profileForm,
    errors,
    needsBlockchainConfirmation,
    
    // 方法
    validateForm,
    clearErrors,
    loadProfile,
    saveProfile,
    resetForm,
    copyAddress
  }
}
