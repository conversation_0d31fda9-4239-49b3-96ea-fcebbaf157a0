/**
 * 钱包功能管理 Composable
 */

import { ref, computed, watch } from 'vue'
import { useMetaMaskStore } from '@/stores/metamask'
import userService from '@/services/userService'

export function useWallet() {
  const metamaskStore = useMetaMaskStore()
  
  const balance = ref(0)
  const loading = ref(false)
  const transactions = ref([])
  const transactionsLoading = ref(false)

  const formattedBalance = computed(() => {
    return parseFloat(balance.value).toFixed(4)
  })

  // 加载钱包余额
  const loadBalance = async () => {
    if (!metamaskStore.isConnected || !metamaskStore.currentAccount) return
    
    loading.value = true
    
    try {
      // 尝试从 API 获取余额
      const apiBalance = await userService.getWalletBalance(metamaskStore.currentAccount)
      balance.value = parseFloat(apiBalance)
    } catch (error) {
      console.warn('API 获取余额失败，使用 MetaMask 余额:', error)
      // 如果 API 调用失败，使用 MetaMask 的余额
      balance.value = parseFloat(metamaskStore.balance) || 0
    } finally {
      loading.value = false
    }
  }

  // 加载交易记录
  const loadTransactions = async (options = {}) => {
    if (!metamaskStore.currentAccount) return
    
    transactionsLoading.value = true
    
    try {
      // 尝试从 API 获取交易记录
      const apiTransactions = await userService.getTransactionHistory(
        metamaskStore.currentAccount, 
        options
      )
      transactions.value = apiTransactions
    } catch (error) {
      console.warn('API 获取交易记录失败，使用模拟数据:', error)
      
      // 如果 API 调用失败，使用模拟数据
      const mockTransactions = [
        {
          id: 'TX001',
          type: 'income',
          description: '专利出售收入',
          patentName: '智能家居控制系统专利',
          amount: '5.2500',
          time: '2024-01-15 14:30:25',
          status: '已确认',
          txHash: '0x1234567890abcdef1234567890abcdef12345678'
        },
        {
          id: 'TX002',
          type: 'expense',
          description: '购买专利',
          patentName: '区块链数据存储专利',
          amount: '2.1000',
          time: '2024-01-14 09:15:42',
          status: '已确认',
          txHash: '0xabcdef1234567890abcdef1234567890abcdef12'
        },
        {
          id: 'TX003',
          type: 'income',
          description: '钱包充值',
          patentName: null,
          amount: '10.0000',
          time: '2024-01-13 16:45:18',
          status: '已确认',
          txHash: '0x567890abcdef1234567890abcdef1234567890ab'
        }
      ]
      
      // 应用筛选
      let filteredTransactions = mockTransactions
      
      if (options.type) {
        filteredTransactions = filteredTransactions.filter(tx => tx.type === options.type)
      }
      
      if (options.keyword) {
        const keyword = options.keyword.toLowerCase()
        filteredTransactions = filteredTransactions.filter(tx =>
          tx.description.toLowerCase().includes(keyword) ||
          (tx.patentName && tx.patentName.toLowerCase().includes(keyword))
        )
      }
      
      transactions.value = filteredTransactions
    } finally {
      transactionsLoading.value = false
    }
  }

  // 获取最近交易记录
  const getRecentTransactions = async (limit = 5) => {
    await loadTransactions({ limit })
    return transactions.value.slice(0, limit)
  }

  // 模拟充值
  const simulateRecharge = async (amount) => {
    try {
      // 尝试调用 API
      await userService.simulateRecharge(metamaskStore.currentAccount, amount)
      
      // 更新本地余额
      balance.value += parseFloat(amount)
      
      // 刷新交易记录
      await loadTransactions()
      
      return true
    } catch (error) {
      console.warn('API 充值失败，使用本地模拟:', error)
      
      // 如果 API 调用失败，模拟充值成功
      await new Promise(resolve => setTimeout(resolve, 2000))
      balance.value += parseFloat(amount)
      
      // 添加模拟交易记录
      const newTransaction = {
        id: `TX${Date.now()}`,
        type: 'income',
        description: '钱包充值',
        patentName: null,
        amount: amount,
        time: new Date().toLocaleString('zh-CN'),
        status: '已确认',
        txHash: `0x${Math.random().toString(16).substr(2, 40)}`
      }
      
      transactions.value.unshift(newTransaction)
      
      return true
    }
  }

  // 模拟提现
  const simulateWithdraw = async (amount) => {
    const withdrawAmount = parseFloat(amount)
    
    if (withdrawAmount > balance.value) {
      throw new Error('余额不足')
    }
    
    try {
      // 尝试调用 API
      await userService.simulateWithdraw(metamaskStore.currentAccount, amount)
      
      // 更新本地余额
      balance.value -= withdrawAmount
      
      // 刷新交易记录
      await loadTransactions()
      
      return true
    } catch (error) {
      console.warn('API 提现失败，使用本地模拟:', error)
      
      // 如果 API 调用失败，模拟提现成功
      await new Promise(resolve => setTimeout(resolve, 2000))
      balance.value -= withdrawAmount
      
      // 添加模拟交易记录
      const newTransaction = {
        id: `TX${Date.now()}`,
        type: 'expense',
        description: '钱包提现',
        patentName: null,
        amount: amount,
        time: new Date().toLocaleString('zh-CN'),
        status: '已确认',
        txHash: `0x${Math.random().toString(16).substr(2, 40)}`
      }
      
      transactions.value.unshift(newTransaction)
      
      return true
    }
  }

  // 获取交易详情
  const getTransactionDetail = async (transactionId) => {
    try {
      return await userService.getTransactionDetail(transactionId)
    } catch (error) {
      console.warn('API 获取交易详情失败:', error)
      // 从本地交易记录中查找
      return transactions.value.find(tx => tx.id === transactionId)
    }
  }

  // 监听钱包连接状态
  watch(() => metamaskStore.isConnected, (connected) => {
    if (connected) {
      loadBalance()
      loadTransactions()
    } else {
      balance.value = 0
      transactions.value = []
    }
  })

  // 监听账户变化
  watch(() => metamaskStore.currentAccount, () => {
    if (metamaskStore.currentAccount) {
      loadBalance()
      loadTransactions()
    }
  })

  return {
    // 状态
    balance,
    formattedBalance,
    loading,
    transactions,
    transactionsLoading,
    
    // 方法
    loadBalance,
    loadTransactions,
    getRecentTransactions,
    simulateRecharge,
    simulateWithdraw,
    getTransactionDetail
  }
}
