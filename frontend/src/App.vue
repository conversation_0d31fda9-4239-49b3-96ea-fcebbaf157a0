<script>
import WalletStatus from './components/WalletStatus.vue'

export default {
  name: 'App',
  components: {
    WalletStatus
  }
}
</script>

<template>
  <div id="app">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-shield-check me-2"></i>
          区块链专利交易系统
        </router-link>
        
        <button 
          class="navbar-toggler" 
          type="button" 
          data-bs-toggle="collapse" 
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <router-link class="nav-link" to="/">
                <i class="bi bi-house me-1"></i>
                首页
              </router-link>
            </li>
            <li class="nav-item">
              <router-link class="nav-link" to="/wallet">
                <i class="bi bi-wallet2 me-1"></i>
                钱包
              </router-link>
            </li>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                <i class="bi bi-file-earmark-text me-1"></i>
                专利管理
              </a>
              <ul class="dropdown-menu">
                <li><router-link class="dropdown-item" to="/patent/search">专利搜索</router-link></li>
                <li><router-link class="dropdown-item" to="/patent/upload">专利上传</router-link></li>
                <li><router-link class="dropdown-item" to="/my-patents/published">我的专利</router-link></li>
              </ul>
            </li>
          </ul>
          
          <!-- Wallet Status -->
          <div class="navbar-nav">
            <div class="nav-item">
              <WalletStatus />
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid">
      <div class="row">
        <!-- Sidebar (if needed for future modules) -->
        <div class="col-md-2 d-none d-md-block bg-light min-vh-100 pt-3">
          <div class="sidebar">
            <h6 class="text-muted text-uppercase fw-bold px-3 mb-3">快速操作</h6>
            <ul class="nav nav-pills flex-column">
              <li class="nav-item">
                <router-link class="nav-link" to="/profile">
                  <i class="bi bi-person me-2"></i>
                  个人信息
                </router-link>
              </li>
              <li class="nav-item">
                <router-link class="nav-link" to="/notifications">
                  <i class="bi bi-bell me-2"></i>
                  通知中心
                </router-link>
              </li>
            </ul>
          </div>
        </div>
        
        <!-- Main Content Area -->
        <div class="col-md-10 offset-md-2">
          <div class="content-area p-4">
            <router-view />
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
      <div class="container">
        <p class="mb-0">
          &copy; 2024 区块链专利交易系统 - 基于区块链技术的专利保护与交易平台
        </p>
      </div>
    </footer>
  </div>
</template>

<style>
#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

.navbar-brand {
  font-weight: bold;
  font-size: 1.25rem;
}

.sidebar {
  position: sticky;
  top: 20px;
}

.sidebar .nav-link {
  color: #6c757d;
  border-radius: 0.375rem;
  margin-bottom: 0.25rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.router-link-active {
  background-color: #0d6efd;
  color: white;
}

.content-area {
  min-height: calc(100vh - 200px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Bootstrap customizations */
.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.text-primary {
  color: #0d6efd !important;
}

.bg-primary {
  background-color: #0d6efd !important;
}

/* Animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
