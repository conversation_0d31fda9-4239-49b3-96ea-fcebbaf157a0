<template>
  <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-warning text-dark">
          <h5 class="modal-title">
            <i class="bi bi-exclamation-triangle me-2"></i>
            交易确认
          </h5>
          <button type="button" class="btn-close" @click="$emit('cancel')"></button>
        </div>
        <div class="modal-body">
          <!-- 交易摘要 -->
          <div class="alert alert-info">
            <h6><i class="bi bi-info-circle me-2"></i>交易摘要</h6>
            <p class="mb-0">您即将购买专利 "{{ patent.name }}"，请仔细核对以下信息：</p>
          </div>

          <!-- 专利信息 -->
          <div class="card mb-3">
            <div class="card-header">
              <h6 class="mb-0">专利信息</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-8">
                  <p><strong>专利名称:</strong> {{ patent.name }}</p>
                  <p><strong>专利号:</strong> {{ patent.number }}</p>
                  <p><strong>专利类别:</strong> {{ patent.category }}</p>
                </div>
                <div class="col-md-4">
                  <div class="text-center">
                    <h4 class="text-success">{{ patent.price }} ETH</h4>
                    <small class="text-muted">交易价格</small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 交易详情 -->
          <div class="card mb-3">
            <div class="card-header">
              <h6 class="mb-0">交易详情</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-6">
                  <div class="d-flex justify-content-between">
                    <span>专利价格:</span>
                    <span class="fw-bold">{{ patent.price }} ETH</span>
                  </div>
                </div>
                <div class="col-6">
                  <div class="d-flex justify-content-between">
                    <span>预估手续费:</span>
                    <span class="fw-bold">{{ tradeData.gasFee }} ETH</span>
                  </div>
                </div>
              </div>
              <hr>
              <div class="d-flex justify-content-between">
                <span class="h6">总计:</span>
                <span class="h5 text-primary fw-bold">{{ tradeData.total }} ETH</span>
              </div>
            </div>
          </div>

          <!-- 卖方信息 -->
          <div class="card mb-3">
            <div class="card-header">
              <h6 class="mb-0">卖方信息</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <p><strong>姓名:</strong> {{ patent.owner.name }}</p>
                  <p><strong>钱包地址:</strong> {{ formatAddress(patent.owner.address) }}</p>
                </div>
                <div class="col-md-6">
                  <p><strong>联系电话:</strong> {{ patent.owner.phone || '未提供' }}</p>
                  <p><strong>信誉评级:</strong> 
                    <span class="badge bg-success">优秀</span>
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 交易条款 -->
          <div class="card mb-3">
            <div class="card-header">
              <h6 class="mb-0">交易条款</h6>
            </div>
            <div class="card-body">
              <div class="form-check mb-2">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="agreeTerms" 
                  v-model="agreeTerms"
                >
                <label class="form-check-label" for="agreeTerms">
                  我同意《专利交易服务协议》和《区块链交易条款》
                </label>
              </div>
              <div class="form-check mb-2">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="understandRisk" 
                  v-model="understandRisk"
                >
                <label class="form-check-label" for="understandRisk">
                  我理解区块链交易的不可逆性，一旦确认无法撤销
                </label>
              </div>
              <div class="form-check">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="confirmInfo" 
                  v-model="confirmInfo"
                >
                <label class="form-check-label" for="confirmInfo">
                  我已仔细核对所有交易信息，确认无误
                </label>
              </div>
            </div>
          </div>

          <!-- 安全验证 -->
          <div class="card mb-3">
            <div class="card-header">
              <h6 class="mb-0">安全验证</h6>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <label for="password" class="form-label">请输入钱包密码确认身份:</label>
                <input 
                  type="password" 
                  class="form-control" 
                  id="password" 
                  v-model="password"
                  placeholder="输入钱包密码"
                  :class="{ 'is-invalid': passwordError }"
                >
                <div v-if="passwordError" class="invalid-feedback">
                  {{ passwordError }}
                </div>
              </div>
              
              <!-- 验证码 -->
              <div class="row">
                <div class="col-8">
                  <label for="captcha" class="form-label">验证码:</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    id="captcha" 
                    v-model="captcha"
                    placeholder="输入验证码"
                    :class="{ 'is-invalid': captchaError }"
                  >
                  <div v-if="captchaError" class="invalid-feedback">
                    {{ captchaError }}
                  </div>
                </div>
                <div class="col-4">
                  <label class="form-label">&nbsp;</label>
                  <div 
                    class="captcha-code d-flex align-items-center justify-content-center border rounded"
                    @click="refreshCaptcha"
                    style="height: 38px; cursor: pointer; background-color: #f8f9fa;"
                  >
                    <span class="fw-bold text-primary">{{ captchaCode }}</span>
                  </div>
                  <small class="text-muted">点击刷新</small>
                </div>
              </div>
            </div>
          </div>

          <!-- 最终确认 -->
          <div class="alert alert-warning">
            <h6><i class="bi bi-exclamation-triangle me-2"></i>最终确认</h6>
            <p class="mb-0">
              点击"确认交易"后，系统将调用智能合约执行交易。
              交易一旦提交到区块链网络，将无法撤销。请确保您已仔细核对所有信息。
            </p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('cancel')">
            <i class="bi bi-x-circle me-1"></i>
            取消交易
          </button>
          <button 
            type="button" 
            class="btn btn-success" 
            @click="confirmTrade"
            :disabled="!canConfirm || confirming"
          >
            <i class="bi bi-check-circle me-1"></i>
            {{ confirming ? '处理中...' : '确认交易' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'TradeConfirmation',
  props: {
    patent: {
      type: Object,
      required: true
    },
    tradeData: {
      type: Object,
      required: true
    }
  },
  emits: ['confirm', 'cancel'],
  setup(props, { emit }) {
    const agreeTerms = ref(false)
    const understandRisk = ref(false)
    const confirmInfo = ref(false)
    const password = ref('')
    const captcha = ref('')
    const captchaCode = ref('')
    const confirming = ref(false)
    const passwordError = ref('')
    const captchaError = ref('')

    // 生成验证码
    const generateCaptcha = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      let result = ''
      for (let i = 0; i < 4; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    }

    // 初始化验证码
    captchaCode.value = generateCaptcha()

    // 刷新验证码
    const refreshCaptcha = () => {
      captchaCode.value = generateCaptcha()
      captcha.value = ''
      captchaError.value = ''
    }

    // 检查是否可以确认
    const canConfirm = computed(() => {
      return agreeTerms.value && 
             understandRisk.value && 
             confirmInfo.value && 
             password.value.length >= 6 && 
             captcha.value.length === 4
    })

    // 确认交易
    const confirmTrade = async () => {
      // 重置错误信息
      passwordError.value = ''
      captchaError.value = ''

      // 验证密码
      if (password.value.length < 6) {
        passwordError.value = '密码长度至少6位'
        return
      }

      // 验证验证码
      if (captcha.value.toUpperCase() !== captchaCode.value) {
        captchaError.value = '验证码错误'
        return
      }

      confirming.value = true

      try {
        // 模拟验证过程
        await new Promise(resolve => setTimeout(resolve, 1500))

        // 构造交易数据
        const transactionData = {
          patentId: props.patent.id,
          price: props.patent.price,
          gasFee: props.tradeData.gasFee,
          total: props.tradeData.total,
          password: password.value,
          timestamp: new Date().toISOString()
        }

        emit('confirm', transactionData)
      } catch (error) {
        console.error('交易确认失败:', error)
        alert('交易确认失败，请重试')
      } finally {
        confirming.value = false
      }
    }

    // 格式化地址
    const formatAddress = (address) => {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    return {
      agreeTerms,
      understandRisk,
      confirmInfo,
      password,
      captcha,
      captchaCode,
      confirming,
      passwordError,
      captchaError,
      canConfirm,
      confirmTrade,
      refreshCaptcha,
      formatAddress
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1060;
}

.captcha-code {
  font-family: 'Courier New', monospace;
  font-size: 1.2rem;
  letter-spacing: 2px;
}

.form-check-label {
  font-size: 0.95rem;
  line-height: 1.4;
}

.card-header h6 {
  font-weight: 600;
}

.alert h6 {
  margin-bottom: 0.5rem;
}
</style>
