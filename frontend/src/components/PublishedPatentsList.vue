<template>
  <div class="published-patents-list">
    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="mt-2 text-muted">正在加载专利列表...</p>
    </div>

    <!-- 无数据 -->
    <div v-else-if="patents.length === 0" class="text-center py-5">
      <i class="bi bi-inbox display-1 text-muted"></i>
      <h5 class="mt-3 text-muted">暂无发布的专利</h5>
      <p class="text-muted">您还没有发布任何专利</p>
      <button
        class="btn btn-primary"
        @click="$router.push('/patent/upload')"
      >
        <i class="bi bi-plus-circle"></i>
        立即上传专利
      </button>
    </div>

    <!-- 专利列表 -->
    <div v-else class="patents-grid">
      <div
        v-for="patent in patents"
        :key="patent.id"
        class="patent-item"
      >
        <div class="card h-100">
          <div class="card-header">
            <div class="d-flex justify-content-between align-items-start">
              <h6 class="card-title mb-0" :title="patent.patentName">
                {{ truncateText(patent.patentName, 40) }}
              </h6>
              <span class="badge" :class="getStatusBadgeClass(patent.status)">
                {{ getStatusText(patent.status) }}
              </span>
            </div>
            <small class="text-muted">专利号: {{ patent.patentNumber }}</small>
          </div>
          
          <div class="card-body">
            <div class="patent-info">
              <div class="info-row">
                <span class="info-label">分类:</span>
                <span class="info-value">{{ getCategoryText(patent.category) }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">价格:</span>
                <span class="info-value text-success">{{ patent.price }} ETH</span>
              </div>
              <div class="info-row">
                <span class="info-label">上传时间:</span>
                <span class="info-value">{{ formatDate(patent.createdAt) }}</span>
              </div>
              <div v-if="patent.viewCount !== undefined" class="info-row">
                <span class="info-label">浏览次数:</span>
                <span class="info-value">{{ patent.viewCount }}</span>
              </div>
            </div>
          </div>
          
          <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <button
                class="btn btn-outline-primary btn-sm"
                @click="viewDetail(patent)"
              >
                <i class="bi bi-eye"></i>
                查看详情
              </button>
              
              <div class="dropdown">
                <button
                  class="btn btn-outline-secondary btn-sm dropdown-toggle"
                  type="button"
                  :id="`dropdownMenuButton${patent.id}`"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <i class="bi bi-three-dots"></i>
                  操作
                </button>
                <ul class="dropdown-menu" :aria-labelledby="`dropdownMenuButton${patent.id}`">
                  <!-- 冻结/解冻 -->
                  <li v-if="patent.status === 'APPROVED'">
                    <button
                      class="dropdown-item"
                      @click="freezePatent(patent)"
                    >
                      <i class="bi bi-pause-circle text-warning"></i>
                      冻结专利
                    </button>
                  </li>
                  <li v-if="patent.status === 'FROZEN'">
                    <button
                      class="dropdown-item"
                      @click="unfreezePatent(patent)"
                    >
                      <i class="bi bi-play-circle text-success"></i>
                      解冻专利
                    </button>
                  </li>
                  
                  <!-- 重新上架 -->
                  <li v-if="patent.status === 'SOLD'">
                    <button
                      class="dropdown-item"
                      @click="showRelistModal(patent)"
                    >
                      <i class="bi bi-arrow-repeat text-info"></i>
                      重新上架
                    </button>
                  </li>
                  
                  <!-- 撤销专利 -->
                  <li v-if="['PENDING', 'APPROVED', 'FROZEN'].includes(patent.status)">
                    <hr class="dropdown-divider">
                    <button
                      class="dropdown-item text-danger"
                      @click="revokePatent(patent)"
                    >
                      <i class="bi bi-trash"></i>
                      撤销专利
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 重新上架模态框 -->
    <div
      class="modal fade"
      id="relistModal"
      tabindex="-1"
      aria-labelledby="relistModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="relistModalLabel">
              重新上架专利
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedPatent" class="mb-3">
              <h6>{{ selectedPatent.patentName }}</h6>
              <p class="text-muted">专利号: {{ selectedPatent.patentNumber }}</p>
              <p class="text-muted">原价格: {{ selectedPatent.price }} ETH</p>
            </div>
            
            <div class="mb-3">
              <label for="newPrice" class="form-label">新价格 (ETH) *</label>
              <input
                type="number"
                class="form-control"
                id="newPrice"
                v-model="newPrice"
                min="0"
                step="0.001"
                placeholder="请输入新价格"
                required
              >
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              取消
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="confirmRelist"
              :disabled="!newPrice || parseFloat(newPrice) <= 0"
            >
              确认上架
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Modal } from 'bootstrap'

export default {
  name: 'PublishedPatentsList',
  props: {
    patents: {
      type: Array,
      required: true,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['status-change', 'revoke', 'relist'],
  setup(props, { emit }) {
    const router = useRouter()

    // 响应式数据
    const selectedPatent = ref(null)
    const newPrice = ref('')

    let relistModal = null

    // 工具方法
    const truncateText = (text, maxLength) => {
      if (!text) return ''
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    const getCategoryText = (category) => {
      const categoryMap = {
        invention: '发明专利',
        utility: '实用新型专利',
        design: '外观设计专利',
        software: '软件著作权',
        trademark: '商标',
      }
      return categoryMap[category] || category
    }

    const getStatusText = (status) => {
      const statusMap = {
        PENDING: '待审核',
        APPROVED: '已上架',
        REJECTED: '已拒绝',
        TRADING: '交易中',
        SOLD: '已售出',
        FROZEN: '已冻结',
        REVOKED: '已撤销',
      }
      return statusMap[status] || status
    }

    const getStatusBadgeClass = (status) => {
      const classMap = {
        PENDING: 'bg-warning',
        APPROVED: 'bg-success',
        REJECTED: 'bg-danger',
        TRADING: 'bg-info',
        SOLD: 'bg-secondary',
        FROZEN: 'bg-dark',
        REVOKED: 'bg-danger',
      }
      return classMap[status] || 'bg-secondary'
    }

    // 事件处理
    const viewDetail = (patent) => {
      router.push(`/patent/${patent.id}`)
    }

    const freezePatent = (patent) => {
      if (confirm('确定要冻结这个专利吗？冻结后将暂停交易。')) {
        emit('status-change', patent.id, 'FROZEN')
      }
    }

    const unfreezePatent = (patent) => {
      if (confirm('确定要解冻这个专利吗？解冻后将恢复交易。')) {
        emit('status-change', patent.id, 'APPROVED')
      }
    }

    const revokePatent = (patent) => {
      emit('revoke', patent.id)
    }

    const showRelistModal = (patent) => {
      selectedPatent.value = patent
      newPrice.value = patent.price

      // 初始化模态框
      if (!relistModal) {
        const modalElement = document.getElementById('relistModal')
        if (modalElement) {
          relistModal = new Modal(modalElement)
        }
      }

      if (relistModal) {
        relistModal.show()
      }
    }

    const confirmRelist = () => {
      if (selectedPatent.value && newPrice.value) {
        emit('relist', selectedPatent.value.id, parseFloat(newPrice.value))
        
        if (relistModal) {
          relistModal.hide()
        }
        
        // 重置数据
        selectedPatent.value = null
        newPrice.value = ''
      }
    }

    return {
      selectedPatent,
      newPrice,
      truncateText,
      formatDate,
      getCategoryText,
      getStatusText,
      getStatusBadgeClass,
      viewDetail,
      freezePatent,
      unfreezePatent,
      revokePatent,
      showRelistModal,
      confirmRelist,
    }
  },
}
</script>

<style scoped>
.published-patents-list {
  width: 100%;
}

.patents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.patent-item .card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.patent-item .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-title {
  color: #495057;
  font-weight: 600;
  font-size: 1rem;
}

.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.patent-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-row {
  display: flex;
  justify-content: between;
  font-size: 0.875rem;
}

.info-label {
  color: #6c757d;
  font-weight: 500;
  min-width: 5rem;
}

.info-value {
  color: #495057;
  margin-left: auto;
}

.card-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 0.5rem 0.5rem !important;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.375rem;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item i {
  width: 1rem;
  margin-right: 0.5rem;
}

.text-success {
  color: #198754 !important;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .patents-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .card-footer .d-flex {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .btn-sm {
    width: 100%;
  }
}
</style>
