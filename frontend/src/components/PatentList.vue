<template>
  <div class="patent-list">
    <div class="row">
      <div
        v-for="patent in patents"
        :key="patent.id"
        class="col-lg-6 col-xl-4 mb-4"
      >
        <PatentCard
          :patent="patent"
          @view-detail="$emit('view-detail', patent)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import PatentCard from './PatentCard.vue'

export default {
  name: 'PatentList',
  components: {
    PatentCard,
  },
  props: {
    patents: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  emits: ['view-detail'],
}
</script>

<style scoped>
.patent-list {
  width: 100%;
}

@media (max-width: 768px) {
  .col-lg-6,
  .col-xl-4 {
    margin-bottom: 1rem;
  }
}
</style>
