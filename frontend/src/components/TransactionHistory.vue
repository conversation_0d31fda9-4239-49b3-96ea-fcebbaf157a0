<template>
  <div class="modal fade" :class="{ show: show }" :style="{ display: show ? 'block' : 'none' }" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">
            <i class="bi bi-clock-history me-2"></i>
            交易历史记录
          </h5>
          <button type="button" class="btn-close btn-close-white" @click="$emit('close')"></button>
        </div>
        <div class="modal-body">
          <!-- 筛选器 -->
          <div class="row mb-4">
            <div class="col-md-3">
              <label class="form-label">交易类型</label>
              <select class="form-select" v-model="filters.type">
                <option value="">全部类型</option>
                <option value="income">收入</option>
                <option value="expense">支出</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">时间范围</label>
              <select class="form-select" v-model="filters.timeRange">
                <option value="">全部时间</option>
                <option value="today">今天</option>
                <option value="week">最近一周</option>
                <option value="month">最近一月</option>
                <option value="quarter">最近三月</option>
              </select>
            </div>
            <div class="col-md-4">
              <label class="form-label">搜索关键词</label>
              <input
                type="text"
                class="form-control"
                v-model="filters.keyword"
                placeholder="搜索专利名称或交易描述"
              >
            </div>
            <div class="col-md-2 d-flex align-items-end">
              <button class="btn btn-outline-primary w-100" @click="applyFilters">
                <i class="bi bi-search me-1"></i>
                筛选
              </button>
            </div>
          </div>

          <!-- 交易列表 -->
          <div v-if="loading" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载交易记录...</p>
          </div>

          <div v-else-if="filteredTransactions.length === 0" class="text-center py-5 text-muted">
            <i class="bi bi-inbox display-1 mb-3"></i>
            <h5>暂无交易记录</h5>
            <p>您还没有任何交易记录</p>
          </div>

          <div v-else class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>时间</th>
                  <th>类型</th>
                  <th>描述</th>
                  <th>专利名称</th>
                  <th>金额 (ETH)</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="transaction in paginatedTransactions" :key="transaction.id">
                  <td>
                    <div class="small">{{ formatDate(transaction.time) }}</div>
                    <div class="text-muted smaller">{{ formatTime(transaction.time) }}</div>
                  </td>
                  <td>
                    <span
                      :class="[
                        'badge',
                        transaction.type === 'income' ? 'bg-success' : 'bg-danger'
                      ]"
                    >
                      <i
                        :class="[
                          'bi me-1',
                          transaction.type === 'income' ? 'bi-arrow-down' : 'bi-arrow-up'
                        ]"
                      ></i>
                      {{ transaction.type === 'income' ? '收入' : '支出' }}
                    </span>
                  </td>
                  <td>{{ transaction.description }}</td>
                  <td>
                    <span v-if="transaction.patentName" class="text-primary">
                      {{ transaction.patentName }}
                    </span>
                    <span v-else class="text-muted">-</span>
                  </td>
                  <td>
                    <span
                      :class="[
                        'fw-bold',
                        transaction.type === 'income' ? 'text-success' : 'text-danger'
                      ]"
                    >
                      {{ transaction.type === 'income' ? '+' : '-' }}{{ transaction.amount }}
                    </span>
                  </td>
                  <td>
                    <span
                      :class="[
                        'badge',
                        getStatusClass(transaction.status)
                      ]"
                    >
                      {{ transaction.status }}
                    </span>
                  </td>
                  <td>
                    <button
                      class="btn btn-sm btn-outline-primary"
                      @click="viewTransactionDetail(transaction)"
                    >
                      <i class="bi bi-eye"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页 -->
          <nav v-if="totalPages > 1" class="mt-4">
            <ul class="pagination justify-content-center">
              <li class="page-item" :class="{ disabled: currentPage === 1 }">
                <button class="page-link" @click="changePage(currentPage - 1)">
                  <i class="bi bi-chevron-left"></i>
                </button>
              </li>
              <li
                v-for="page in visiblePages"
                :key="page"
                class="page-item"
                :class="{ active: page === currentPage }"
              >
                <button class="page-link" @click="changePage(page)">{{ page }}</button>
              </li>
              <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                <button class="page-link" @click="changePage(currentPage + 1)">
                  <i class="bi bi-chevron-right"></i>
                </button>
              </li>
            </ul>
          </nav>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">关闭</button>
          <button type="button" class="btn btn-primary" @click="exportTransactions">
            <i class="bi bi-download me-2"></i>
            导出记录
          </button>
        </div>
      </div>
    </div>
  </div>
  <div v-if="show" class="modal-backdrop fade show"></div>

  <!-- 交易详情模态框 -->
  <div class="modal fade" :class="{ show: showDetailModal }" :style="{ display: showDetailModal ? 'block' : 'none' }" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">交易详情</h5>
          <button type="button" class="btn-close" @click="showDetailModal = false"></button>
        </div>
        <div class="modal-body" v-if="selectedTransaction">
          <div class="row">
            <div class="col-sm-4 fw-bold">交易ID:</div>
            <div class="col-sm-8">{{ selectedTransaction.id }}</div>
          </div>
          <hr>
          <div class="row">
            <div class="col-sm-4 fw-bold">时间:</div>
            <div class="col-sm-8">{{ selectedTransaction.time }}</div>
          </div>
          <hr>
          <div class="row">
            <div class="col-sm-4 fw-bold">类型:</div>
            <div class="col-sm-8">
              <span
                :class="[
                  'badge',
                  selectedTransaction.type === 'income' ? 'bg-success' : 'bg-danger'
                ]"
              >
                {{ selectedTransaction.type === 'income' ? '收入' : '支出' }}
              </span>
            </div>
          </div>
          <hr>
          <div class="row">
            <div class="col-sm-4 fw-bold">描述:</div>
            <div class="col-sm-8">{{ selectedTransaction.description }}</div>
          </div>
          <hr v-if="selectedTransaction.patentName">
          <div v-if="selectedTransaction.patentName" class="row">
            <div class="col-sm-4 fw-bold">专利名称:</div>
            <div class="col-sm-8">{{ selectedTransaction.patentName }}</div>
          </div>
          <hr>
          <div class="row">
            <div class="col-sm-4 fw-bold">金额:</div>
            <div class="col-sm-8">
              <span
                :class="[
                  'fw-bold',
                  selectedTransaction.type === 'income' ? 'text-success' : 'text-danger'
                ]"
              >
                {{ selectedTransaction.type === 'income' ? '+' : '-' }}{{ selectedTransaction.amount }} ETH
              </span>
            </div>
          </div>
          <hr>
          <div class="row">
            <div class="col-sm-4 fw-bold">状态:</div>
            <div class="col-sm-8">
              <span
                :class="[
                  'badge',
                  getStatusClass(selectedTransaction.status)
                ]"
              >
                {{ selectedTransaction.status }}
              </span>
            </div>
          </div>
          <hr v-if="selectedTransaction.txHash">
          <div v-if="selectedTransaction.txHash" class="row">
            <div class="col-sm-4 fw-bold">交易哈希:</div>
            <div class="col-sm-8">
              <code class="small">{{ selectedTransaction.txHash }}</code>
              <button
                class="btn btn-sm btn-outline-secondary ms-2"
                @click="copyToClipboard(selectedTransaction.txHash)"
              >
                <i class="bi bi-clipboard"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="showDetailModal = false">关闭</button>
        </div>
      </div>
    </div>
  </div>
  <div v-if="showDetailModal" class="modal-backdrop fade show"></div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useMetaMaskStore } from '@/stores/metamask'

export default {
  name: 'TransactionHistory',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const metamaskStore = useMetaMaskStore()

    const loading = ref(false)
    const showDetailModal = ref(false)
    const selectedTransaction = ref(null)
    const currentPage = ref(1)
    const pageSize = 10

    const filters = reactive({
      type: '',
      timeRange: '',
      keyword: ''
    })

    const transactions = ref([])

    // 模拟交易数据
    const mockTransactions = [
      {
        id: 'TX001',
        type: 'income',
        description: '专利出售收入',
        patentName: '智能家居控制系统专利',
        amount: '5.2500',
        time: '2024-01-15 14:30:25',
        status: '已确认',
        txHash: '0x1234567890abcdef1234567890abcdef12345678'
      },
      {
        id: 'TX002',
        type: 'expense',
        description: '购买专利',
        patentName: '区块链数据存储专利',
        amount: '2.1000',
        time: '2024-01-14 09:15:42',
        status: '已确认',
        txHash: '0xabcdef1234567890abcdef1234567890abcdef12'
      },
      {
        id: 'TX003',
        type: 'income',
        description: '钱包充值',
        patentName: null,
        amount: '10.0000',
        time: '2024-01-13 16:45:18',
        status: '已确认',
        txHash: '0x567890abcdef1234567890abcdef1234567890ab'
      },
      {
        id: 'TX004',
        type: 'expense',
        description: '专利权保护申请费用',
        patentName: '人工智能算法专利',
        amount: '0.5000',
        time: '2024-01-12 11:20:33',
        status: '处理中',
        txHash: null
      },
      {
        id: 'TX005',
        type: 'income',
        description: '专利授权收入',
        patentName: '物联网通信协议专利',
        amount: '3.7500',
        time: '2024-01-11 08:45:12',
        status: '已确认',
        txHash: '******************************************'
      }
    ]

    // 筛选后的交易
    const filteredTransactions = computed(() => {
      let result = transactions.value

      if (filters.type) {
        result = result.filter(tx => tx.type === filters.type)
      }

      if (filters.timeRange) {
        const now = new Date()
        const filterDate = new Date()

        switch (filters.timeRange) {
          case 'today':
            filterDate.setHours(0, 0, 0, 0)
            break
          case 'week':
            filterDate.setDate(now.getDate() - 7)
            break
          case 'month':
            filterDate.setMonth(now.getMonth() - 1)
            break
          case 'quarter':
            filterDate.setMonth(now.getMonth() - 3)
            break
        }

        result = result.filter(tx => new Date(tx.time) >= filterDate)
      }

      if (filters.keyword) {
        const keyword = filters.keyword.toLowerCase()
        result = result.filter(tx =>
          tx.description.toLowerCase().includes(keyword) ||
          (tx.patentName && tx.patentName.toLowerCase().includes(keyword))
        )
      }

      return result
    })

    // 分页相关计算
    const totalPages = computed(() => Math.ceil(filteredTransactions.value.length / pageSize))

    const paginatedTransactions = computed(() => {
      const start = (currentPage.value - 1) * pageSize
      const end = start + pageSize
      return filteredTransactions.value.slice(start, end)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })

    // 加载交易记录
    const loadTransactions = async () => {
      if (!metamaskStore.currentAccount) return

      loading.value = true

      try {
        // TODO: 调用 API 获取交易记录
        // const response = await fetch(`/api/transactions/${metamaskStore.currentAccount}`)
        // const data = await response.json()

        // 使用模拟数据
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟加载延迟
        transactions.value = mockTransactions
      } catch (error) {
        console.error('加载交易记录失败:', error)
      } finally {
        loading.value = false
      }
    }

    // 应用筛选
    const applyFilters = () => {
      currentPage.value = 1
    }

    // 切换页面
    const changePage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }

    // 查看交易详情
    const viewTransactionDetail = (transaction) => {
      selectedTransaction.value = transaction
      showDetailModal.value = true
    }

    // 获取状态样式类
    const getStatusClass = (status) => {
      switch (status) {
        case '已确认':
          return 'bg-success'
        case '处理中':
          return 'bg-warning'
        case '失败':
          return 'bg-danger'
        default:
          return 'bg-secondary'
      }
    }

    // 格式化日期
    const formatDate = (dateTime) => {
      const date = new Date(dateTime)
      return date.toLocaleDateString('zh-CN')
    }

    // 格式化时间
    const formatTime = (dateTime) => {
      const date = new Date(dateTime)
      return date.toLocaleTimeString('zh-CN')
    }

    // 复制到剪贴板
    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        // TODO: 显示成功提示
      } catch (err) {
        console.error('复制失败:', err)
      }
    }

    // 导出交易记录
    const exportTransactions = () => {
      const csvContent = [
        ['时间', '类型', '描述', '专利名称', '金额(ETH)', '状态', '交易哈希'].join(','),
        ...filteredTransactions.value.map(tx => [
          tx.time,
          tx.type === 'income' ? '收入' : '支出',
          tx.description,
          tx.patentName || '',
          tx.amount,
          tx.status,
          tx.txHash || ''
        ].join(','))
      ].join('\n')

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `交易记录_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    // 监听显示状态
    watch(() => props.show, (show) => {
      if (show) {
        loadTransactions()
      }
    })

    onMounted(() => {
      if (props.show) {
        loadTransactions()
      }
    })

    return {
      loading,
      showDetailModal,
      selectedTransaction,
      currentPage,
      filters,
      filteredTransactions,
      paginatedTransactions,
      totalPages,
      visiblePages,
      applyFilters,
      changePage,
      viewTransactionDetail,
      getStatusClass,
      formatDate,
      formatTime,
      copyToClipboard,
      exportTransactions
    }
  }
}
</script>

<style scoped>
.modal-content {
  border-radius: 15px;
}

.modal-header {
  border-radius: 15px 15px 0 0 !important;
}

.table {
  border-radius: 10px;
  overflow: hidden;
}

.table th {
  background-color: #f8f9fa;
  border: none;
  font-weight: 600;
  color: #495057;
}

.table td {
  border: none;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.pagination .page-link {
  border-radius: 8px;
  margin: 0 2px;
  border: 1px solid #dee2e6;
  color: #6c757d;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.form-select:focus,
.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.smaller {
  font-size: 0.75rem;
}

.modal-backdrop {
  z-index: 1040;
}

.modal {
  z-index: 1050;
}

@media (max-width: 768px) {
  .modal-dialog {
    margin: 0.5rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-sm {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
  }
}
</style>
