<template>
  <div class="patent-card">
    <div class="card h-100">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-start">
          <h6 class="card-title mb-0" :title="patent.patentName">
            {{ truncateText(patent.patentName, 50) }}
          </h6>
          <span class="badge" :class="getStatusBadgeClass(patent.status)">
            {{ getStatusText(patent.status) }}
          </span>
        </div>
        <small class="text-muted">专利号: {{ patent.patentNumber }}</small>
      </div>
      
      <div class="card-body">
        <div class="patent-info">
          <div class="info-item">
            <i class="bi bi-tag"></i>
            <span>{{ getCategoryText(patent.category) }}</span>
          </div>
          
          <div class="info-item">
            <i class="bi bi-currency-exchange"></i>
            <span class="price">{{ patent.price }} ETH</span>
          </div>
          
          <div class="info-item">
            <i class="bi bi-person"></i>
            <span>{{ patent.ownerName }}</span>
          </div>
          
          <div class="info-item">
            <i class="bi bi-calendar"></i>
            <span>{{ formatDate(patent.applicationDate) }}</span>
          </div>
          
          <div v-if="patent.isAgency" class="info-item">
            <i class="bi bi-building"></i>
            <span class="text-warning">代理销售</span>
          </div>
        </div>
        
        <div class="patent-summary mt-3">
          <p class="text-muted mb-0" :title="patent.summary">
            {{ truncateText(patent.summary, 100) }}
          </p>
        </div>
      </div>
      
      <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
          <small class="text-muted">
            上传时间: {{ formatDate(patent.createdAt) }}
          </small>
          <button
            class="btn btn-primary btn-sm"
            @click="$emit('view-detail', patent)"
          >
            <i class="bi bi-eye"></i>
            查看详情
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PatentCard',
  props: {
    patent: {
      type: Object,
      required: true,
    },
  },
  emits: ['view-detail'],
  methods: {
    // 截断文本
    truncateText(text, maxLength) {
      if (!text) return ''
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },
    
    // 获取分类文本
    getCategoryText(category) {
      const categoryMap = {
        invention: '发明专利',
        utility: '实用新型专利',
        design: '外观设计专利',
        software: '软件著作权',
        trademark: '商标',
      }
      return categoryMap[category] || category
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        PENDING: '待审核',
        APPROVED: '已上架',
        REJECTED: '已拒绝',
        TRADING: '交易中',
        SOLD: '已售出',
        FROZEN: '已冻结',
        REVOKED: '已撤销',
      }
      return statusMap[status] || status
    },
    
    // 获取状态徽章样式
    getStatusBadgeClass(status) {
      const classMap = {
        PENDING: 'bg-warning',
        APPROVED: 'bg-success',
        REJECTED: 'bg-danger',
        TRADING: 'bg-info',
        SOLD: 'bg-secondary',
        FROZEN: 'bg-dark',
        REVOKED: 'bg-danger',
      }
      return classMap[status] || 'bg-secondary'
    },
  },
}
</script>

<style scoped>
.patent-card {
  height: 100%;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
  padding: 1rem;
}

.card-title {
  color: #495057;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.4;
}

.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.card-body {
  padding: 1rem;
}

.patent-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.info-item i {
  width: 1rem;
  text-align: center;
  color: #0d6efd;
}

.price {
  font-weight: 600;
  color: #198754;
}

.patent-summary {
  border-top: 1px solid #dee2e6;
  padding-top: 0.75rem;
}

.patent-summary p {
  font-size: 0.875rem;
  line-height: 1.4;
}

.card-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 0.5rem 0.5rem !important;
  padding: 0.75rem 1rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: #fff;
  text-decoration: none;
  transition: all 0.15s ease-in-out;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
  transform: translateY(-1px);
}

.text-warning {
  color: #f57c00 !important;
  font-weight: 500;
}

@media (max-width: 768px) {
  .card-header,
  .card-body,
  .card-footer {
    padding: 0.75rem;
  }
  
  .card-title {
    font-size: 0.9rem;
  }
  
  .info-item {
    font-size: 0.8rem;
  }
  
  .patent-summary p {
    font-size: 0.8rem;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .btn-sm {
    width: 100%;
    padding: 0.5rem;
  }
}
</style>
