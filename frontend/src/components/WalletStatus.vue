<template>
  <div class="wallet-status">
    <!-- Not Connected -->
    <button 
      v-if="!metamaskStore.isConnected" 
      @click="connectWallet"
      class="btn btn-outline-light btn-sm"
      :disabled="metamaskStore.isConnecting || !metamaskStore.isMetaMaskInstalled"
    >
      <span v-if="metamaskStore.isConnecting" class="spinner-border spinner-border-sm me-1" role="status">
        <span class="visually-hidden">连接中...</span>
      </span>
      <i v-else class="bi bi-wallet2 me-1"></i>
      {{ metamaskStore.isConnecting ? '连接中...' : '连接钱包' }}
    </button>

    <!-- Connected -->
    <div v-else class="dropdown">
      <button 
        class="btn btn-success btn-sm dropdown-toggle d-flex align-items-center"
        type="button" 
        data-bs-toggle="dropdown"
      >
        <i class="bi bi-check-circle-fill me-2"></i>
        <div class="text-start">
          <div class="fw-bold">{{ metamaskStore.shortAddress }}</div>
          <small class="opacity-75">{{ parseFloat(metamaskStore.balance).toFixed(4) }} ETH</small>
        </div>
      </button>
      
      <ul class="dropdown-menu dropdown-menu-end">
        <li>
          <div class="dropdown-header">
            <i class="bi bi-person-circle me-2"></i>
            钱包信息
          </div>
        </li>
        <li>
          <div class="dropdown-item-text">
            <div class="small text-muted mb-1">账户地址</div>
            <div class="d-flex align-items-center">
              <code class="small me-2">{{ metamaskStore.shortAddress }}</code>
              <button 
                @click="copyAddress"
                class="btn btn-outline-secondary btn-xs"
                title="复制完整地址"
              >
                <i class="bi bi-copy"></i>
              </button>
            </div>
          </div>
        </li>
        <li>
          <div class="dropdown-item-text">
            <div class="small text-muted mb-1">网络</div>
            <span 
              class="badge"
              :class="metamaskStore.isGanacheNetwork ? 'bg-success' : 'bg-warning'"
            >
              {{ metamaskStore.networkName }}
            </span>
          </div>
        </li>
        <li>
          <div class="dropdown-item-text">
            <div class="small text-muted mb-1">余额</div>
            <div class="fw-bold text-primary">
              {{ parseFloat(metamaskStore.balance).toFixed(4) }} ETH
            </div>
          </div>
        </li>
        <li><hr class="dropdown-divider"></li>
        <li>
          <button 
            @click="refreshData"
            class="dropdown-item"
            :disabled="isRefreshing"
          >
            <i class="bi bi-arrow-clockwise me-2" :class="{ 'spin': isRefreshing }"></i>
            刷新数据
          </button>
        </li>
        <li>
          <router-link to="/wallet" class="dropdown-item">
            <i class="bi bi-wallet2 me-2"></i>
            钱包管理
          </router-link>
        </li>
        <li>
          <router-link to="/profile" class="dropdown-item">
            <i class="bi bi-person me-2"></i>
            个人信息
          </router-link>
        </li>
        <li><hr class="dropdown-divider"></li>
        <li>
          <button 
            @click="metamaskStore.disconnect"
            class="dropdown-item text-danger"
          >
            <i class="bi bi-power me-2"></i>
            断开连接
          </button>
        </li>
      </ul>
    </div>

    <!-- Network Warning Badge -->
    <div 
      v-if="metamaskStore.isConnected && !metamaskStore.isGanacheNetwork"
      class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning"
      title="请切换到 Ganache 网络"
    >
      <i class="bi bi-exclamation-triangle-fill"></i>
    </div>

    <!-- Copy Success Toast -->
    <div 
      class="toast-container position-fixed bottom-0 end-0 p-3"
      style="z-index: 1055;"
    >
      <div 
        ref="copyToast"
        class="toast align-items-center text-white bg-success border-0" 
        role="alert"
      >
        <div class="d-flex">
          <div class="toast-body">
            <i class="bi bi-check-circle me-2"></i>
            地址已复制到剪贴板
          </div>
          <button 
            type="button" 
            class="btn-close btn-close-white me-2 m-auto" 
            data-bs-dismiss="toast"
          ></button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useMetaMaskStore } from '@/stores/metamask'
import { Toast } from 'bootstrap'

export default {
  name: 'WalletStatus',
  setup() {
    const metamaskStore = useMetaMaskStore()
    const isRefreshing = ref(false)
    const copyToast = ref(null)

    // Connect wallet
    async function connectWallet() {
      await metamaskStore.connect()
    }

    // Refresh wallet data
    async function refreshData() {
      isRefreshing.value = true
      try {
        await Promise.all([
          metamaskStore.updateNetworkInfo(),
          metamaskStore.updateBalance()
        ])
      } finally {
        isRefreshing.value = false
      }
    }

    // Copy address to clipboard
    async function copyAddress() {
      try {
        await navigator.clipboard.writeText(metamaskStore.currentAccount)
        // Show toast
        if (copyToast.value) {
          const toast = new Toast(copyToast.value)
          toast.show()
        }
      } catch (err) {
        console.error('复制地址失败:', err)
      }
    }

    // Initialize on component mount
    onMounted(() => {
      metamaskStore.init()
    })

    return {
      metamaskStore,
      isRefreshing,
      copyToast,
      connectWallet,
      refreshData,
      copyAddress
    }
  }
}
</script>

<style scoped>
.wallet-status {
  position: relative;
}

.btn-xs {
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  line-height: 1;
  border-radius: 0.25rem;
}

.dropdown-item-text {
  padding: 0.5rem 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

.dropdown-menu {
  min-width: 280px;
}

.dropdown-header {
  font-weight: 600;
  color: #495057;
}

code {
  font-size: 0.75em;
  background-color: #e9ecef;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  word-break: break-all;
}

.badge {
  font-size: 0.65em;
}

.position-absolute {
  z-index: 1;
}
</style> 