<template>
  <div class="modal fade" id="tradeContractModal" tabindex="-1" aria-labelledby="tradeContractModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="tradeContractModalLabel">
            <i class="bi bi-file-earmark-text me-2"></i>
            交易合同
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div v-if="loading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在生成合同...</p>
          </div>
          
          <div v-else-if="contract" class="contract-content">
            <!-- 合同标题 -->
            <div class="text-center mb-4">
              <h4 class="fw-bold">专利交易合同</h4>
              <p class="text-muted">合同编号: {{ contract.contractNumber }}</p>
            </div>

            <!-- 交易双方信息 -->
            <div class="row mb-4">
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">卖方信息</h6>
                  </div>
                  <div class="card-body">
                    <p><strong>姓名:</strong> {{ contract.seller.name }}</p>
                    <p><strong>钱包地址:</strong> {{ formatAddress(contract.seller.address) }}</p>
                    <p><strong>联系电话:</strong> {{ contract.seller.phone || '未提供' }}</p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-success text-white">
                    <h6 class="mb-0">买方信息</h6>
                  </div>
                  <div class="card-body">
                    <p><strong>姓名:</strong> {{ contract.buyer.name }}</p>
                    <p><strong>钱包地址:</strong> {{ formatAddress(contract.buyer.address) }}</p>
                    <p><strong>联系电话:</strong> {{ contract.buyer.phone || '未提供' }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 专利信息 -->
            <div class="card mb-4">
              <div class="card-header bg-info text-white">
                <h6 class="mb-0">专利信息</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <p><strong>专利名称:</strong> {{ contract.patent.name }}</p>
                    <p><strong>专利号:</strong> {{ contract.patent.number }}</p>
                    <p><strong>专利类别:</strong> {{ contract.patent.category }}</p>
                  </div>
                  <div class="col-md-6">
                    <p><strong>申请日期:</strong> {{ formatDate(contract.patent.applicationDate) }}</p>
                    <p><strong>授权日期:</strong> {{ formatDate(contract.patent.grantDate) }}</p>
                    <p><strong>有效期至:</strong> {{ formatDate(contract.patent.expiryDate) }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 交易信息 -->
            <div class="card mb-4">
              <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">交易信息</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <p><strong>交易价格:</strong> {{ contract.trade.price }} ETH</p>
                    <p><strong>交易时间:</strong> {{ formatDateTime(contract.trade.timestamp) }}</p>
                  </div>
                  <div class="col-md-6">
                    <p><strong>交易哈希:</strong> {{ formatAddress(contract.trade.transactionHash) }}</p>
                    <p><strong>区块高度:</strong> {{ contract.trade.blockNumber }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 合同条款 -->
            <div class="card mb-4">
              <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">合同条款</h6>
              </div>
              <div class="card-body">
                <ol>
                  <li>本合同基于区块链智能合约执行，具有不可篡改性。</li>
                  <li>卖方保证对所交易专利拥有完整的所有权，无任何法律纠纷。</li>
                  <li>买方支付约定价格后，专利所有权自动转移至买方。</li>
                  <li>交易完成后，卖方不得再对该专利主张任何权利。</li>
                  <li>双方确认已充分了解区块链交易的不可逆性。</li>
                  <li>本合同受中华人民共和国法律管辖。</li>
                </ol>
              </div>
            </div>

            <!-- 数字签名 -->
            <div class="row">
              <div class="col-md-6">
                <div class="text-center">
                  <p><strong>卖方数字签名</strong></p>
                  <p class="font-monospace small">{{ contract.signatures.seller }}</p>
                  <p class="text-muted small">{{ formatDateTime(contract.trade.timestamp) }}</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="text-center">
                  <p><strong>买方数字签名</strong></p>
                  <p class="font-monospace small">{{ contract.signatures.buyer }}</p>
                  <p class="text-muted small">{{ formatDateTime(contract.trade.timestamp) }}</p>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="text-center py-4">
            <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
            <p class="mt-2">无法加载合同信息</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>
            关闭
          </button>
          <button 
            v-if="contract" 
            type="button" 
            class="btn btn-primary" 
            @click="downloadContract"
            :disabled="downloading"
          >
            <i class="bi bi-download me-1"></i>
            {{ downloading ? '下载中...' : '下载合同' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'

export default {
  name: 'TradeContractModal',
  props: {
    tradeId: {
      type: String,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const loading = ref(false)
    const downloading = ref(false)
    const contract = ref(null)

    // 监听 tradeId 变化，加载合同数据
    watch(() => props.tradeId, async (newTradeId) => {
      if (newTradeId) {
        await loadContract(newTradeId)
      }
    }, { immediate: true })

    // 加载合同数据
    const loadContract = async (tradeId) => {
      if (!tradeId) return
      
      loading.value = true
      try {
        // 模拟 API 调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟合同数据
        contract.value = {
          contractNumber: `CT${Date.now()}`,
          seller: {
            name: '张三',
            address: '0x1234567890123456789012345678901234567890',
            phone: '13800138000'
          },
          buyer: {
            name: '李四',
            address: '0x0987654321098765432109876543210987654321',
            phone: '13900139000'
          },
          patent: {
            name: '一种新型区块链共识算法',
            number: 'CN202100001234.5',
            category: '发明专利',
            applicationDate: '2021-01-15',
            grantDate: '2022-06-20',
            expiryDate: '2041-01-15'
          },
          trade: {
            price: '10.5',
            timestamp: new Date().toISOString(),
            transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
            blockNumber: 12345678
          },
          signatures: {
            seller: '0x1a2b3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890',
            buyer: '0x9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba'
          }
        }
      } catch (error) {
        console.error('加载合同失败:', error)
        contract.value = null
      } finally {
        loading.value = false
      }
    }

    // 格式化地址
    const formatAddress = (address) => {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 格式化日期时间
    const formatDateTime = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }

    // 下载合同
    const downloadContract = async () => {
      if (!contract.value) return
      
      downloading.value = true
      try {
        // 模拟生成 PDF
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // 创建下载链接
        const element = document.createElement('a')
        const file = new Blob([generateContractText()], { type: 'text/plain' })
        element.href = URL.createObjectURL(file)
        element.download = `专利交易合同_${contract.value.contractNumber}.txt`
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
      } catch (error) {
        console.error('下载合同失败:', error)
        alert('下载失败，请重试')
      } finally {
        downloading.value = false
      }
    }

    // 生成合同文本
    const generateContractText = () => {
      const c = contract.value
      return `
专利交易合同

合同编号: ${c.contractNumber}
签署时间: ${formatDateTime(c.trade.timestamp)}

卖方信息:
姓名: ${c.seller.name}
钱包地址: ${c.seller.address}
联系电话: ${c.seller.phone}

买方信息:
姓名: ${c.buyer.name}
钱包地址: ${c.buyer.address}
联系电话: ${c.buyer.phone}

专利信息:
专利名称: ${c.patent.name}
专利号: ${c.patent.number}
专利类别: ${c.patent.category}
申请日期: ${formatDate(c.patent.applicationDate)}
授权日期: ${formatDate(c.patent.grantDate)}
有效期至: ${formatDate(c.patent.expiryDate)}

交易信息:
交易价格: ${c.trade.price} ETH
交易哈希: ${c.trade.transactionHash}
区块高度: ${c.trade.blockNumber}

合同条款:
1. 本合同基于区块链智能合约执行，具有不可篡改性。
2. 卖方保证对所交易专利拥有完整的所有权，无任何法律纠纷。
3. 买方支付约定价格后，专利所有权自动转移至买方。
4. 交易完成后，卖方不得再对该专利主张任何权利。
5. 双方确认已充分了解区块链交易的不可逆性。
6. 本合同受中华人民共和国法律管辖。

数字签名:
卖方签名: ${c.signatures.seller}
买方签名: ${c.signatures.buyer}

本合同由区块链智能合约自动生成，具有法律效力。
      `.trim()
    }

    return {
      loading,
      downloading,
      contract,
      formatAddress,
      formatDate,
      formatDateTime,
      downloadContract
    }
  }
}
</script>

<style scoped>
.contract-content {
  font-size: 14px;
}

.card-header h6 {
  font-weight: 600;
}

.font-monospace {
  font-family: 'Courier New', monospace;
}

.modal-lg {
  max-width: 900px;
}

@media print {
  .modal-header,
  .modal-footer {
    display: none;
  }
  
  .modal-body {
    padding: 0;
  }
}
</style>
