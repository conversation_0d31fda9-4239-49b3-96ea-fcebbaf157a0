<template>
  <div class="modal fade" :class="{ show: show }" :style="{ display: show ? 'block' : 'none' }" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-success text-white">
          <h5 class="modal-title">
            <i class="bi bi-plus-circle me-2"></i>
            钱包充值
          </h5>
          <button type="button" class="btn-close btn-close-white" @click="closeModal"></button>
        </div>
        <div class="modal-body">
          <div class="text-center mb-4">
            <i class="bi bi-wallet2 display-6 text-success mb-3"></i>
            <p class="text-muted">选择充值金额，系统将模拟充值到您的钱包</p>
          </div>

          <form @submit.prevent="handleRecharge">
            <!-- 快捷金额选择 -->
            <div class="mb-3">
              <label class="form-label fw-bold">快捷充值</label>
              <div class="row g-2">
                <div class="col-4" v-for="amount in quickAmounts" :key="amount">
                  <button 
                    type="button" 
                    class="btn btn-outline-success w-100"
                    :class="{ active: selectedAmount === amount }"
                    @click="selectAmount(amount)"
                  >
                    {{ amount }} ETH
                  </button>
                </div>
              </div>
            </div>

            <!-- 自定义金额 -->
            <div class="mb-3">
              <label for="customAmount" class="form-label fw-bold">
                自定义金额 <span class="text-danger">*</span>
              </label>
              <div class="input-group">
                <input 
                  type="number" 
                  class="form-control" 
                  id="customAmount"
                  v-model="rechargeForm.amount"
                  :class="{ 'is-invalid': errors.amount }"
                  placeholder="请输入充值金额"
                  min="0.001"
                  max="100"
                  step="0.001"
                  required
                >
                <span class="input-group-text">ETH</span>
              </div>
              <div v-if="errors.amount" class="invalid-feedback">
                {{ errors.amount }}
              </div>
              <small class="text-muted">最小充值 0.001 ETH，最大充值 100 ETH</small>
            </div>

            <!-- 充值说明 -->
            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              <strong>充值说明：</strong>
              <ul class="mb-0 mt-2">
                <li>这是模拟充值，用于演示系统功能</li>
                <li>实际项目中需要对接真实的支付系统</li>
                <li>充值后金额将立即到账</li>
              </ul>
            </div>

            <!-- 费用预览 -->
            <div v-if="rechargeForm.amount > 0" class="card bg-light mb-3">
              <div class="card-body">
                <h6 class="card-title">费用预览</h6>
                <div class="d-flex justify-content-between">
                  <span>充值金额:</span>
                  <span class="fw-bold">{{ rechargeForm.amount }} ETH</span>
                </div>
                <div class="d-flex justify-content-between">
                  <span>手续费:</span>
                  <span class="text-success">免费</span>
                </div>
                <hr class="my-2">
                <div class="d-flex justify-content-between">
                  <span class="fw-bold">实际到账:</span>
                  <span class="fw-bold text-success">{{ rechargeForm.amount }} ETH</span>
                </div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
              <button 
                type="button" 
                class="btn btn-secondary me-md-2"
                @click="closeModal"
                :disabled="loading"
              >
                取消
              </button>
              <button 
                type="submit" 
                class="btn btn-success"
                :disabled="loading || !rechargeForm.amount"
              >
                <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                {{ loading ? '充值中...' : '确认充值' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div v-if="show" class="modal-backdrop fade show"></div>
</template>

<script>
import { ref, reactive, watch } from 'vue'

export default {
  name: 'RechargeModal',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const loading = ref(false)
    const selectedAmount = ref(null)
    const quickAmounts = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
    
    const rechargeForm = reactive({
      amount: ''
    })
    
    const errors = reactive({
      amount: ''
    })

    // 选择快捷金额
    const selectAmount = (amount) => {
      selectedAmount.value = amount
      rechargeForm.amount = amount.toString()
      clearErrors()
    }

    // 验证表单
    const validateForm = () => {
      clearErrors()
      let isValid = true
      
      const amount = parseFloat(rechargeForm.amount)
      
      if (!rechargeForm.amount) {
        errors.amount = '请输入充值金额'
        isValid = false
      } else if (isNaN(amount) || amount <= 0) {
        errors.amount = '请输入有效的金额'
        isValid = false
      } else if (amount < 0.001) {
        errors.amount = '最小充值金额为 0.001 ETH'
        isValid = false
      } else if (amount > 100) {
        errors.amount = '最大充值金额为 100 ETH'
        isValid = false
      }
      
      return isValid
    }

    const clearErrors = () => {
      errors.amount = ''
    }

    // 处理充值
    const handleRecharge = async () => {
      if (!validateForm()) return
      
      loading.value = true
      
      try {
        // 模拟充值过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // TODO: 实际项目中调用充值 API
        // const response = await fetch('/api/wallet/recharge', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({
        //     amount: rechargeForm.amount,
        //     address: metamaskStore.currentAccount
        //   })
        // })
        
        emit('success', parseFloat(rechargeForm.amount))
        closeModal()
      } catch (error) {
        console.error('充值失败:', error)
        errors.amount = '充值失败，请重试'
      } finally {
        loading.value = false
      }
    }

    // 关闭模态框
    const closeModal = () => {
      if (!loading.value) {
        resetForm()
        emit('close')
      }
    }

    // 重置表单
    const resetForm = () => {
      rechargeForm.amount = ''
      selectedAmount.value = null
      clearErrors()
    }

    // 监听模态框显示状态
    watch(() => props.show, (newShow) => {
      if (!newShow) {
        resetForm()
      }
    })

    // 监听自定义金额输入
    watch(() => rechargeForm.amount, () => {
      const amount = parseFloat(rechargeForm.amount)
      if (quickAmounts.includes(amount)) {
        selectedAmount.value = amount
      } else {
        selectedAmount.value = null
      }
      clearErrors()
    })

    return {
      loading,
      selectedAmount,
      quickAmounts,
      rechargeForm,
      errors,
      selectAmount,
      handleRecharge,
      closeModal
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1055;
}

.modal-backdrop {
  z-index: 1050;
}

.modal-content {
  border-radius: 15px;
  border: none;
}

.modal-header {
  border-radius: 15px 15px 0 0;
}

.btn.active {
  background-color: #198754 !important;
  border-color: #198754 !important;
  color: white !important;
}

.card {
  border-radius: 10px;
}

.alert {
  border-radius: 10px;
}

.form-control:focus {
  border-color: #198754;
  box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn {
  border-radius: 8px;
  font-weight: 500;
}

.btn:hover {
  transform: translateY(-1px);
}

.display-6 {
  opacity: 0.8;
}

@media (max-width: 576px) {
  .modal-dialog {
    margin: 1rem;
  }
  
  .btn {
    font-size: 0.875rem;
  }
}
</style> 