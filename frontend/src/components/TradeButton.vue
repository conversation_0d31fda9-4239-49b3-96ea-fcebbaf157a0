<template>
  <div class="trade-button">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="bi bi-cart"></i>
          购买专利
        </h6>
      </div>
      <div class="card-body">
        <!-- 价格信息 -->
        <div class="price-info mb-3">
          <div class="d-flex justify-content-between align-items-center">
            <span class="text-muted">专利价格:</span>
            <span class="price-value">{{ patent.price }} ETH</span>
          </div>
          <div v-if="usdPrice" class="d-flex justify-content-between align-items-center">
            <span class="text-muted">约合:</span>
            <span class="text-muted">${{ usdPrice }}</span>
          </div>
        </div>

        <!-- 钱包余额检查 -->
        <div class="balance-check mb-3">
          <div class="d-flex justify-content-between align-items-center">
            <span class="text-muted">钱包余额:</span>
            <span :class="balanceClass">{{ walletBalance }} ETH</span>
          </div>
          <div v-if="!hasSufficientBalance" class="alert alert-warning mt-2 py-2">
            <small>
              <i class="bi bi-exclamation-triangle"></i>
              余额不足，请先充值
            </small>
          </div>
        </div>

        <!-- 交易按钮 -->
        <div class="trade-actions">
          <button
            v-if="!isConnected"
            class="btn btn-outline-primary w-100 mb-2"
            @click="connectWallet"
          >
            <i class="bi bi-wallet2"></i>
            连接钱包
          </button>
          
          <button
            v-else-if="!hasSufficientBalance"
            class="btn btn-warning w-100 mb-2"
            @click="goToWallet"
          >
            <i class="bi bi-plus-circle"></i>
            去充值
          </button>
          
          <button
            v-else
            class="btn btn-success w-100 mb-2"
            @click="initiateTrade"
            :disabled="isProcessing"
          >
            <span
              v-if="isProcessing"
              class="spinner-border spinner-border-sm me-2"
              role="status"
              aria-hidden="true"
            ></span>
            <i v-else class="bi bi-cart-check"></i>
            {{ isProcessing ? '处理中...' : '立即购买' }}
          </button>

          <button
            class="btn btn-outline-secondary w-100"
            @click="addToFavorites"
            :disabled="isFavorited"
          >
            <i :class="isFavorited ? 'bi bi-heart-fill' : 'bi bi-heart'"></i>
            {{ isFavorited ? '已收藏' : '收藏' }}
          </button>
        </div>

        <!-- 交易说明 -->
        <div class="trade-info mt-3">
          <small class="text-muted">
            <i class="bi bi-info-circle"></i>
            交易将通过智能合约执行，确保安全可靠
          </small>
        </div>
      </div>
    </div>

    <!-- 交易确认模态框 -->
    <div
      class="modal fade"
      id="tradeConfirmModal"
      tabindex="-1"
      aria-labelledby="tradeConfirmModalLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="tradeConfirmModalLabel">
              确认购买
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :disabled="isProcessing"
            ></button>
          </div>
          <div class="modal-body">
            <div class="trade-summary">
              <h6>交易详情</h6>
              <div class="summary-item">
                <span>专利名称:</span>
                <span>{{ patent.patentName }}</span>
              </div>
              <div class="summary-item">
                <span>专利号:</span>
                <span>{{ patent.patentNumber }}</span>
              </div>
              <div class="summary-item">
                <span>卖方:</span>
                <span>{{ formatAddress(patent.uploaderAddress) }}</span>
              </div>
              <div class="summary-item">
                <span>价格:</span>
                <span class="text-success fw-bold">{{ patent.price }} ETH</span>
              </div>
              <div class="summary-item">
                <span>买方:</span>
                <span>{{ formatAddress(currentAccount) }}</span>
              </div>
            </div>

            <div v-if="isProcessing" class="processing-status mt-3">
              <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                  <span class="visually-hidden">处理中...</span>
                </div>
                <span>{{ processingMessage }}</span>
              </div>
              <div class="progress mt-2">
                <div
                  class="progress-bar"
                  role="progressbar"
                  :style="{ width: processingProgress + '%' }"
                  :aria-valuenow="processingProgress"
                  aria-valuemin="0"
                  aria-valuemax="100"
                ></div>
              </div>
            </div>

            <div v-if="tradeError" class="alert alert-danger mt-3">
              {{ tradeError }}
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
              :disabled="isProcessing"
            >
              取消
            </button>
            <button
              v-if="!isProcessing && !tradeCompleted"
              type="button"
              class="btn btn-success"
              @click="confirmTrade"
            >
              确认购买
            </button>
            <button
              v-if="tradeCompleted"
              type="button"
              class="btn btn-primary"
              data-bs-dismiss="modal"
              @click="handleTradeComplete"
            >
              完成
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMetaMaskStore } from '@/stores/metamask'
import { Modal } from 'bootstrap'

export default {
  name: 'TradeButton',
  props: {
    patent: {
      type: Object,
      required: true,
    },
  },
  emits: ['trade-initiated'],
  setup(props, { emit }) {
    const router = useRouter()
    const metaMaskStore = useMetaMaskStore()

    // 响应式数据
    const isProcessing = ref(false)
    const isFavorited = ref(false)
    const usdPrice = ref('')
    const tradeError = ref('')
    const tradeCompleted = ref(false)
    const processingMessage = ref('')
    const processingProgress = ref(0)

    let tradeConfirmModal = null

    // 计算属性
    const isConnected = computed(() => metaMaskStore.isConnected)
    const currentAccount = computed(() => metaMaskStore.currentAccount)
    const walletBalance = computed(() => metaMaskStore.balance)
    
    const hasSufficientBalance = computed(() => {
      const balance = parseFloat(walletBalance.value || '0')
      const price = parseFloat(props.patent.price || '0')
      return balance >= price
    })

    const balanceClass = computed(() => {
      return hasSufficientBalance.value ? 'text-success' : 'text-danger'
    })

    // 组件挂载时初始化
    onMounted(() => {
      // 初始化模态框
      const modalElement = document.getElementById('tradeConfirmModal')
      if (modalElement) {
        tradeConfirmModal = new Modal(modalElement)
      }

      // 获取USD价格（模拟）
      fetchUsdPrice()
    })

    // 获取USD价格
    const fetchUsdPrice = async () => {
      try {
        // 这里应该调用实际的汇率API
        // 暂时使用模拟数据
        const ethToUsd = 2000 // 假设1 ETH = 2000 USD
        const price = parseFloat(props.patent.price || '0')
        usdPrice.value = (price * ethToUsd).toFixed(2)
      } catch (error) {
        console.warn('获取USD价格失败:', error)
      }
    }

    // 连接钱包
    const connectWallet = async () => {
      try {
        await metaMaskStore.connect()
      } catch (error) {
        console.error('连接钱包失败:', error)
        alert('连接钱包失败，请重试')
      }
    }

    // 跳转到钱包页面
    const goToWallet = () => {
      router.push('/wallet')
    }

    // 发起交易
    const initiateTrade = () => {
      if (!hasSufficientBalance.value) {
        alert('余额不足，请先充值')
        return
      }

      // 显示确认模态框
      if (tradeConfirmModal) {
        tradeConfirmModal.show()
      }
    }

    // 确认交易
    const confirmTrade = async () => {
      try {
        isProcessing.value = true
        tradeError.value = ''
        tradeCompleted.value = false

        // 步骤1: 验证余额
        updateProgress('验证钱包余额...', 20)
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 步骤2: 调用智能合约
        updateProgress('调用智能合约...', 40)
        await new Promise(resolve => setTimeout(resolve, 2000))

        // 步骤3: 等待区块确认
        updateProgress('等待区块确认...', 70)
        await new Promise(resolve => setTimeout(resolve, 3000))

        // 步骤4: 完成交易
        updateProgress('交易完成！', 100)
        tradeCompleted.value = true

        // 发出交易完成事件
        emit('trade-initiated', {
          patentId: props.patent.id,
          buyer: currentAccount.value,
          seller: props.patent.uploaderAddress,
          price: props.patent.price,
        })

      } catch (error) {
        console.error('交易失败:', error)
        tradeError.value = error.message || '交易失败，请重试'
      } finally {
        isProcessing.value = false
      }
    }

    // 更新处理进度
    const updateProgress = (message, progress) => {
      processingMessage.value = message
      processingProgress.value = progress
    }

    // 处理交易完成
    const handleTradeComplete = () => {
      // 跳转到交易详情页面或我的专利页面
      router.push('/my-patents/purchased')
    }

    // 添加到收藏
    const addToFavorites = () => {
      // 这里应该调用收藏API
      isFavorited.value = true
      console.log('添加到收藏:', props.patent.id)
    }

    // 格式化地址
    const formatAddress = (address) => {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    return {
      isProcessing,
      isFavorited,
      usdPrice,
      tradeError,
      tradeCompleted,
      processingMessage,
      processingProgress,
      isConnected,
      currentAccount,
      walletBalance,
      hasSufficientBalance,
      balanceClass,
      connectWallet,
      goToWallet,
      initiateTrade,
      confirmTrade,
      handleTradeComplete,
      addToFavorites,
      formatAddress,
    }
  },
}
</script>

<style scoped>
.trade-button .card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h6 {
  color: #495057;
  font-weight: 600;
}

.price-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #198754;
}

.balance-check .text-success {
  color: #198754 !important;
}

.balance-check .text-danger {
  color: #dc3545 !important;
}

.btn {
  font-weight: 500;
  border-radius: 0.375rem;
}

.btn-success {
  background-color: #198754;
  border-color: #198754;
}

.btn-success:hover {
  background-color: #157347;
  border-color: #146c43;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #000;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.trade-info {
  border-top: 1px solid #dee2e6;
  padding-top: 0.75rem;
}

.trade-summary {
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  padding: 1rem;
}

.summary-item {
  display: flex;
  justify-content: between;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item span:first-child {
  color: #6c757d;
  font-weight: 500;
}

.summary-item span:last-child {
  margin-left: auto;
  color: #495057;
}

.processing-status {
  background-color: #e7f3ff;
  border-radius: 0.375rem;
  padding: 1rem;
}

.progress {
  height: 0.5rem;
  border-radius: 0.25rem;
}

.progress-bar {
  background-color: #0d6efd;
  border-radius: 0.25rem;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: #ffecb5;
  color: #664d03;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c2c7;
  color: #842029;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

@media (max-width: 768px) {
  .trade-summary {
    padding: 0.75rem;
  }
  
  .summary-item {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .summary-item span:last-child {
    margin-left: 0;
  }
}
</style>
