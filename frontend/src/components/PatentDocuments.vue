<template>
  <div class="patent-documents">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="bi bi-file-earmark-text"></i>
          专利文档
        </h5>
      </div>
      <div class="card-body">
        <!-- 主要专利文档 -->
        <div class="document-item">
          <div class="d-flex align-items-center">
            <div class="document-icon me-3">
              <i class="bi bi-file-earmark-pdf text-danger"></i>
            </div>
            <div class="flex-grow-1">
              <h6 class="mb-1">专利文档</h6>
              <p class="text-muted mb-0">
                <small>
                  IPFS: {{ formatHash(patent.documentHash) }}
                  <span class="ms-2">
                    <i class="bi bi-shield-check text-success"></i>
                    区块链验证
                  </span>
                </small>
              </p>
            </div>
            <div class="document-actions">
              <button
                class="btn btn-outline-primary btn-sm me-2"
                @click="viewDocument(patent.documentHash)"
                :disabled="isViewing"
              >
                <span
                  v-if="isViewing"
                  class="spinner-border spinner-border-sm me-1"
                  role="status"
                  aria-hidden="true"
                ></span>
                <i v-else class="bi bi-eye"></i>
                {{ isViewing ? '加载中...' : '预览' }}
              </button>
              <button
                class="btn btn-primary btn-sm"
                @click="downloadDocument(patent.documentHash, 'patent-document')"
                :disabled="isDownloading"
              >
                <span
                  v-if="isDownloading"
                  class="spinner-border spinner-border-sm me-1"
                  role="status"
                  aria-hidden="true"
                ></span>
                <i v-else class="bi bi-download"></i>
                {{ isDownloading ? '下载中...' : '下载' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 代理证明文档 (如果存在) -->
        <div v-if="patent.isAgency && patent.proofDocumentHash" class="document-item">
          <div class="d-flex align-items-center">
            <div class="document-icon me-3">
              <i class="bi bi-file-earmark-check text-warning"></i>
            </div>
            <div class="flex-grow-1">
              <h6 class="mb-1">代理证明文档</h6>
              <p class="text-muted mb-0">
                <small>
                  IPFS: {{ formatHash(patent.proofDocumentHash) }}
                  <span class="ms-2">
                    <i class="bi bi-shield-check text-success"></i>
                    区块链验证
                  </span>
                </small>
              </p>
            </div>
            <div class="document-actions">
              <button
                class="btn btn-outline-primary btn-sm me-2"
                @click="viewDocument(patent.proofDocumentHash)"
                :disabled="isViewing"
              >
                <i class="bi bi-eye"></i>
                预览
              </button>
              <button
                class="btn btn-primary btn-sm"
                @click="downloadDocument(patent.proofDocumentHash, 'proof-document')"
                :disabled="isDownloading"
              >
                <i class="bi bi-download"></i>
                下载
              </button>
            </div>
          </div>
        </div>

        <!-- 文档安全提示 -->
        <div class="alert alert-info mt-3">
          <div class="d-flex align-items-start">
            <i class="bi bi-info-circle me-2 mt-1"></i>
            <div>
              <h6 class="alert-heading mb-1">文档安全说明</h6>
              <p class="mb-0">
                所有文档均存储在去中心化的 IPFS 网络中，并通过区块链技术确保文档的完整性和不可篡改性。
                文档哈希值已记录在智能合约中，可随时验证文档真实性。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文档预览模态框 -->
    <div
      class="modal fade"
      id="documentPreviewModal"
      tabindex="-1"
      aria-labelledby="documentPreviewModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="documentPreviewModalLabel">
              文档预览
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div v-if="previewLoading" class="text-center py-5">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
              </div>
              <p class="mt-2 text-muted">正在加载文档...</p>
            </div>
            <div v-else-if="previewError" class="alert alert-danger">
              <h6 class="alert-heading">预览失败</h6>
              <p class="mb-0">{{ previewError }}</p>
            </div>
            <div v-else-if="previewUrl" class="document-preview">
              <!-- 根据文档类型显示不同的预览 -->
              <iframe
                v-if="isPreviewable"
                :src="previewUrl"
                class="w-100"
                style="height: 600px; border: none;"
              ></iframe>
              <div v-else class="text-center py-5">
                <i class="bi bi-file-earmark display-1 text-muted"></i>
                <h5 class="mt-3">此文档类型不支持在线预览</h5>
                <p class="text-muted">请下载文档到本地查看</p>
                <button
                  class="btn btn-primary"
                  @click="downloadCurrentDocument"
                >
                  <i class="bi bi-download"></i>
                  下载文档
                </button>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              关闭
            </button>
            <button
              v-if="isPreviewable"
              type="button"
              class="btn btn-primary"
              @click="downloadCurrentDocument"
            >
              <i class="bi bi-download"></i>
              下载文档
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { Modal } from 'bootstrap'
import patentService from '@/services/patentService'

export default {
  name: 'PatentDocuments',
  props: {
    patent: {
      type: Object,
      required: true,
    },
  },
  emits: ['download'],
  setup(props, { emit }) {
    // 响应式数据
    const isViewing = ref(false)
    const isDownloading = ref(false)
    const previewLoading = ref(false)
    const previewError = ref('')
    const previewUrl = ref('')
    const currentDocumentHash = ref('')
    const currentDocumentName = ref('')
    const isPreviewable = ref(false)

    let documentPreviewModal = null

    // 格式化哈希值显示
    const formatHash = (hash) => {
      if (!hash) return ''
      return `${hash.slice(0, 8)}...${hash.slice(-8)}`
    }

    // 预览文档
    const viewDocument = async (documentHash) => {
      try {
        isViewing.value = true
        previewLoading.value = true
        previewError.value = ''
        currentDocumentHash.value = documentHash

        // 初始化模态框
        if (!documentPreviewModal) {
          const modalElement = document.getElementById('documentPreviewModal')
          if (modalElement) {
            documentPreviewModal = new Modal(modalElement)
          }
        }

        // 显示模态框
        if (documentPreviewModal) {
          documentPreviewModal.show()
        }

        // 获取文档
        const blob = await patentService.downloadFromIPFS(documentHash)
        const url = URL.createObjectURL(blob)
        
        // 检查文档类型
        const fileType = blob.type
        isPreviewable.value = fileType.includes('pdf') || 
                             fileType.includes('image') || 
                             fileType.includes('text')

        if (isPreviewable.value) {
          previewUrl.value = url
        }

      } catch (error) {
        console.error('预览文档失败:', error)
        previewError.value = error.message || '预览失败，请稍后重试'
      } finally {
        isViewing.value = false
        previewLoading.value = false
      }
    }

    // 下载文档
    const downloadDocument = async (documentHash, fileName) => {
      try {
        isDownloading.value = true

        const blob = await patentService.downloadFromIPFS(documentHash)
        
        // 创建下载链接
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${fileName}-${documentHash.slice(0, 8)}`
        
        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        // 清理URL对象
        URL.revokeObjectURL(url)

        // 发出下载事件
        emit('download', documentHash)

      } catch (error) {
        console.error('下载文档失败:', error)
        alert('下载失败，请稍后重试')
      } finally {
        isDownloading.value = false
      }
    }

    // 下载当前预览的文档
    const downloadCurrentDocument = () => {
      if (currentDocumentHash.value) {
        downloadDocument(currentDocumentHash.value, currentDocumentName.value || 'document')
      }
    }

    return {
      isViewing,
      isDownloading,
      previewLoading,
      previewError,
      previewUrl,
      isPreviewable,
      formatHash,
      viewDocument,
      downloadDocument,
      downloadCurrentDocument,
    }
  },
}
</script>

<style scoped>
.patent-documents .card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h5 {
  color: #495057;
  font-weight: 600;
}

.document-item {
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  background-color: #fff;
}

.document-item:last-of-type {
  margin-bottom: 0;
}

.document-icon {
  font-size: 2rem;
}

.document-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.alert-info {
  background-color: #cff4fc;
  border-color: #b6effb;
  color: #055160;
}

.alert-heading {
  color: inherit;
  font-weight: 600;
}

.modal-xl {
  max-width: 90%;
}

.document-preview {
  min-height: 400px;
}

@media (max-width: 768px) {
  .document-item {
    padding: 0.75rem;
  }
  
  .d-flex.align-items-center {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }
  
  .document-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .btn-sm {
    flex: 1;
  }
  
  .modal-xl {
    max-width: 95%;
  }
}
</style>
