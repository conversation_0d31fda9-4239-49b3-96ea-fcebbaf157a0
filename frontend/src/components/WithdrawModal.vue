<template>
  <div class="modal fade" :class="{ show: show }" :style="{ display: show ? 'block' : 'none' }" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-warning text-dark">
          <h5 class="modal-title">
            <i class="bi bi-dash-circle me-2"></i>
            钱包提现
          </h5>
          <button type="button" class="btn-close" @click="closeModal"></button>
        </div>
        <div class="modal-body">
          <div class="text-center mb-4">
            <i class="bi bi-cash-stack display-6 text-warning mb-3"></i>
            <p class="text-muted">从您的钱包提现到外部地址</p>
          </div>

          <!-- 当前余额显示 -->
          <div class="alert alert-info mb-4">
            <div class="d-flex justify-content-between align-items-center">
              <span><i class="bi bi-wallet me-2"></i>当前余额:</span>
              <span class="fw-bold">{{ maxAmount.toFixed(4) }} ETH</span>
            </div>
          </div>

          <form @submit.prevent="handleWithdraw">
            <!-- 提现金额 -->
            <div class="mb-3">
              <label for="withdrawAmount" class="form-label fw-bold">
                提现金额 <span class="text-danger">*</span>
              </label>
              <div class="input-group">
                <input 
                  type="number" 
                  class="form-control" 
                  id="withdrawAmount"
                  v-model="withdrawForm.amount"
                  :class="{ 'is-invalid': errors.amount }"
                  placeholder="请输入提现金额"
                  min="0.001"
                  :max="maxAmount"
                  step="0.001"
                  required
                >
                <span class="input-group-text">ETH</span>
                <button 
                  type="button" 
                  class="btn btn-outline-secondary"
                  @click="setMaxAmount"
                >
                  全部
                </button>
              </div>
              <div v-if="errors.amount" class="invalid-feedback">
                {{ errors.amount }}
              </div>
              <small class="text-muted">最小提现 0.001 ETH，最大可提现 {{ maxAmount.toFixed(4) }} ETH</small>
            </div>

            <!-- 提现地址 -->
            <div class="mb-3">
              <label for="withdrawAddress" class="form-label fw-bold">
                提现地址 <span class="text-danger">*</span>
              </label>
              <input 
                type="text" 
                class="form-control" 
                id="withdrawAddress"
                v-model="withdrawForm.address"
                :class="{ 'is-invalid': errors.address }"
                placeholder="请输入以太坊地址 (0x...)"
                required
              >
              <div v-if="errors.address" class="invalid-feedback">
                {{ errors.address }}
              </div>
              <small class="text-muted">请确保地址正确，提现无法撤销</small>
            </div>

            <!-- 提现说明 -->
            <div class="alert alert-warning">
              <i class="bi bi-exclamation-triangle me-2"></i>
              <strong>提现说明：</strong>
              <ul class="mb-0 mt-2">
                <li>这是模拟提现，用于演示系统功能</li>
                <li>实际项目中需要通过智能合约执行</li>
                <li>请仔细核对提现地址，操作不可逆</li>
                <li>提现将收取少量 Gas 费用</li>
              </ul>
            </div>

            <!-- 费用预览 -->
            <div v-if="withdrawForm.amount > 0" class="card bg-light mb-3">
              <div class="card-body">
                <h6 class="card-title">费用预览</h6>
                <div class="d-flex justify-content-between">
                  <span>提现金额:</span>
                  <span class="fw-bold">{{ withdrawForm.amount }} ETH</span>
                </div>
                <div class="d-flex justify-content-between">
                  <span>Gas 费用:</span>
                  <span class="text-muted">{{ estimatedGasFee }} ETH</span>
                </div>
                <hr class="my-2">
                <div class="d-flex justify-content-between">
                  <span class="fw-bold">实际到账:</span>
                  <span class="fw-bold text-success">{{ actualAmount }} ETH</span>
                </div>
              </div>
            </div>

            <!-- 安全确认 -->
            <div class="mb-3">
              <div class="form-check">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="confirmWithdraw"
                  v-model="withdrawForm.confirmed"
                  :class="{ 'is-invalid': errors.confirmed }"
                >
                <label class="form-check-label" for="confirmWithdraw">
                  我已确认提现地址正确，了解操作不可撤销
                </label>
                <div v-if="errors.confirmed" class="invalid-feedback">
                  {{ errors.confirmed }}
                </div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
              <button 
                type="button" 
                class="btn btn-secondary me-md-2"
                @click="closeModal"
                :disabled="loading"
              >
                取消
              </button>
              <button 
                type="submit" 
                class="btn btn-warning"
                :disabled="loading || !withdrawForm.amount || !withdrawForm.confirmed"
              >
                <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                {{ loading ? '提现中...' : '确认提现' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div v-if="show" class="modal-backdrop fade show"></div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'

export default {
  name: 'WithdrawModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    maxAmount: {
      type: Number,
      default: 0
    }
  },
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const loading = ref(false)
    
    const withdrawForm = reactive({
      amount: '',
      address: '',
      confirmed: false
    })
    
    const errors = reactive({
      amount: '',
      address: '',
      confirmed: ''
    })

    const estimatedGasFee = computed(() => {
      // 模拟 Gas 费用计算
      return '0.0021'
    })

    const actualAmount = computed(() => {
      const amount = parseFloat(withdrawForm.amount) || 0
      const gasFee = parseFloat(estimatedGasFee.value)
      return Math.max(0, amount - gasFee).toFixed(4)
    })

    // 设置最大金额
    const setMaxAmount = () => {
      withdrawForm.amount = props.maxAmount.toString()
      clearErrors()
    }

    // 验证以太坊地址
    const isValidEthereumAddress = (address) => {
      return /^0x[a-fA-F0-9]{40}$/.test(address)
    }

    // 验证表单
    const validateForm = () => {
      clearErrors()
      let isValid = true
      
      const amount = parseFloat(withdrawForm.amount)
      
      // 验证金额
      if (!withdrawForm.amount) {
        errors.amount = '请输入提现金额'
        isValid = false
      } else if (isNaN(amount) || amount <= 0) {
        errors.amount = '请输入有效的金额'
        isValid = false
      } else if (amount < 0.001) {
        errors.amount = '最小提现金额为 0.001 ETH'
        isValid = false
      } else if (amount > props.maxAmount) {
        errors.amount = `提现金额不能超过余额 ${props.maxAmount.toFixed(4)} ETH`
        isValid = false
      }

      // 验证地址
      if (!withdrawForm.address) {
        errors.address = '请输入提现地址'
        isValid = false
      } else if (!isValidEthereumAddress(withdrawForm.address)) {
        errors.address = '请输入有效的以太坊地址'
        isValid = false
      }

      // 验证确认
      if (!withdrawForm.confirmed) {
        errors.confirmed = '请确认您已了解提现风险'
        isValid = false
      }
      
      return isValid
    }

    const clearErrors = () => {
      Object.keys(errors).forEach(key => {
        errors[key] = ''
      })
    }

    // 处理提现
    const handleWithdraw = async () => {
      if (!validateForm()) return
      
      loading.value = true
      
      try {
        // 模拟提现过程
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        // TODO: 实际项目中调用智能合约
        // const contract = new web3.eth.Contract(ABI, CONTRACT_ADDRESS)
        // await contract.methods.withdraw(
        //   web3.utils.toWei(withdrawForm.amount, 'ether'),
        //   withdrawForm.address
        // ).send({ from: currentAccount })
        
        emit('success', parseFloat(withdrawForm.amount))
        closeModal()
      } catch (error) {
        console.error('提现失败:', error)
        errors.amount = '提现失败，请重试'
      } finally {
        loading.value = false
      }
    }

    // 关闭模态框
    const closeModal = () => {
      if (!loading.value) {
        resetForm()
        emit('close')
      }
    }

    // 重置表单
    const resetForm = () => {
      withdrawForm.amount = ''
      withdrawForm.address = ''
      withdrawForm.confirmed = false
      clearErrors()
    }

    // 监听模态框显示状态
    watch(() => props.show, (newShow) => {
      if (!newShow) {
        resetForm()
      }
    })

    return {
      loading,
      withdrawForm,
      errors,
      estimatedGasFee,
      actualAmount,
      setMaxAmount,
      handleWithdraw,
      closeModal
    }
  }
}
</script>

<style scoped>
.modal {
  z-index: 1055;
}

.modal-backdrop {
  z-index: 1050;
}

.modal-content {
  border-radius: 15px;
  border: none;
}

.modal-header {
  border-radius: 15px 15px 0 0;
}

.card {
  border-radius: 10px;
}

.alert {
  border-radius: 10px;
}

.form-control:focus {
  border-color: #ffc107;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.form-check-input:focus {
  border-color: #ffc107;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.btn {
  border-radius: 8px;
  font-weight: 500;
}

.btn:hover {
  transform: translateY(-1px);
}

.display-6 {
  opacity: 0.8;
}

.input-group .btn {
  border-radius: 0 8px 8px 0;
}

@media (max-width: 576px) {
  .modal-dialog {
    margin: 1rem;
  }
  
  .btn {
    font-size: 0.875rem;
  }
}
</style> 