<template>
  <div class="metamask-connector">
    <!-- MetaMask Not Installed Warning -->
    <div 
      v-if="!metamaskStore.isMetaMaskInstalled" 
      class="alert alert-warning d-flex align-items-center mb-3"
      role="alert"
    >
      <i class="bi bi-exclamation-triangle-fill me-2"></i>
      <div>
        <strong>需要安装 MetaMask</strong>
        <p class="mb-0">
          请先安装 MetaMask 钱包扩展程序。
          <a href="https://metamask.io/download/" target="_blank" class="alert-link">
            点击此处下载
          </a>
        </p>
      </div>
    </div>

    <!-- Connection Error -->
    <div 
      v-if="metamaskStore.error" 
      class="alert alert-danger d-flex align-items-center mb-3"
      role="alert"
    >
      <i class="bi bi-exclamation-circle-fill me-2"></i>
      <div>
        <strong>连接错误</strong>
        <p class="mb-0">{{ metamaskStore.error }}</p>
      </div>
    </div>

    <!-- Connected Status -->
    <div v-if="metamaskStore.isConnected" class="card border-success">
      <div class="card-header bg-success text-white d-flex align-items-center">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span>钱包已连接</span>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <!-- Account Info -->
          <div class="col-md-6">
            <h6 class="card-subtitle mb-2 text-muted">
              <i class="bi bi-wallet2 me-1"></i>
              账户地址
            </h6>
            <div class="d-flex align-items-center">
              <code class="bg-light px-2 py-1 rounded me-2">{{ metamaskStore.shortAddress }}</code>
              <button 
                @click="copyAddress"
                class="btn btn-outline-secondary btn-sm"
                title="复制完整地址"
              >
                <i class="bi bi-copy"></i>
              </button>
            </div>
          </div>

          <!-- Balance -->
          <div class="col-md-6">
            <h6 class="card-subtitle mb-2 text-muted">
              <i class="bi bi-coin me-1"></i>
              余额
            </h6>
            <div class="fw-bold text-primary">
              {{ parseFloat(metamaskStore.balance).toFixed(4) }} ETH
            </div>
          </div>

          <!-- Network Info -->
          <div class="col-12">
            <h6 class="card-subtitle mb-2 text-muted">
              <i class="bi bi-globe me-1"></i>
              网络
            </h6>
            <div class="d-flex align-items-center">
              <span 
                class="badge me-2"
                :class="metamaskStore.isGanacheNetwork ? 'bg-success' : 'bg-warning'"
              >
                {{ metamaskStore.networkName }}
              </span>
              <NetworkValidator />
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-3 d-flex gap-2 flex-wrap">
          <button 
            @click="refreshData"
            class="btn btn-outline-primary btn-sm"
            :disabled="isRefreshing"
          >
            <i class="bi bi-arrow-clockwise me-1" :class="{ 'spin': isRefreshing }"></i>
            刷新
          </button>
          
          <button 
            @click="metamaskStore.disconnect"
            class="btn btn-outline-danger btn-sm"
          >
            <i class="bi bi-power me-1"></i>
            断开连接
          </button>
        </div>
      </div>
    </div>

    <!-- Not Connected -->
    <div v-else class="card border-primary">
      <div class="card-header bg-primary text-white d-flex align-items-center">
        <i class="bi bi-wallet me-2"></i>
        <span>连接 MetaMask 钱包</span>
      </div>
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="bi bi-wallet2 display-1 text-primary opacity-50"></i>
        </div>
        <h5 class="card-title">连接您的钱包</h5>
        <p class="card-text text-muted">
          点击下方按钮连接您的 MetaMask 钱包，开始使用区块链专利交易系统。
        </p>
        <button 
          @click="connectWallet"
          class="btn btn-primary btn-lg"
          :disabled="metamaskStore.isConnecting || !metamaskStore.isMetaMaskInstalled"
        >
          <span v-if="metamaskStore.isConnecting" class="spinner-border spinner-border-sm me-2" role="status">
            <span class="visually-hidden">连接中...</span>
          </span>
          <i v-else class="bi bi-wallet2 me-2"></i>
          {{ metamaskStore.isConnecting ? '连接中...' : '连接钱包' }}
        </button>
      </div>
    </div>

    <!-- Copy Success Toast -->
    <div 
      class="toast-container position-fixed bottom-0 end-0 p-3"
      id="toastContainer"
    >
      <div 
        ref="copyToast"
        class="toast align-items-center text-white bg-success border-0" 
        role="alert"
      >
        <div class="d-flex">
          <div class="toast-body">
            <i class="bi bi-check-circle me-2"></i>
            地址已复制到剪贴板
          </div>
          <button 
            type="button" 
            class="btn-close btn-close-white me-2 m-auto" 
            data-bs-dismiss="toast"
          ></button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useMetaMaskStore } from '@/stores/metamask'
import NetworkValidator from './NetworkValidator.vue'
import { Toast } from 'bootstrap'

export default {
  name: 'MetaMaskConnector',
  components: {
    NetworkValidator
  },
  setup() {
    const metamaskStore = useMetaMaskStore()
    const isRefreshing = ref(false)
    const copyToast = ref(null)

    // Connect wallet
    async function connectWallet() {
      await metamaskStore.connect()
    }

    // Refresh wallet data
    async function refreshData() {
      isRefreshing.value = true
      try {
        await Promise.all([
          metamaskStore.updateNetworkInfo(),
          metamaskStore.updateBalance()
        ])
      } finally {
        isRefreshing.value = false
      }
    }

    // Copy address to clipboard
    async function copyAddress() {
      try {
        await navigator.clipboard.writeText(metamaskStore.currentAccount)
        // Show toast
        if (copyToast.value) {
          const toast = new Toast(copyToast.value)
          toast.show()
        }
      } catch (err) {
        console.error('复制地址失败:', err)
      }
    }

    // Initialize on component mount
    onMounted(() => {
      metamaskStore.init()
    })

    return {
      metamaskStore,
      isRefreshing,
      copyToast,
      connectWallet,
      refreshData,
      copyAddress
    }
  }
}
</script>

<style scoped>
.metamask-connector {
  max-width: 600px;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

code {
  font-size: 0.875em;
  word-break: break-all;
}

.badge {
  font-size: 0.75em;
}

.display-1 {
  font-size: 4rem;
}

.toast-container {
  z-index: 1055;
}
</style> 