<template>
  <div class="patent-events">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="bi bi-clock-history"></i>
          专利事件历史
        </h5>
      </div>
      <div class="card-body">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">正在加载事件历史...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="alert alert-danger">
          <h6 class="alert-heading">加载失败</h6>
          <p class="mb-2">{{ error }}</p>
          <button class="btn btn-outline-danger btn-sm" @click="loadEvents">
            重新加载
          </button>
        </div>

        <!-- 无事件 -->
        <div v-else-if="events.length === 0" class="text-center py-4">
          <i class="bi bi-clock display-4 text-muted"></i>
          <h6 class="mt-3 text-muted">暂无事件记录</h6>
          <p class="text-muted">专利事件将在这里显示</p>
        </div>

        <!-- 事件时间线 -->
        <div v-else class="timeline">
          <div
            v-for="(event, index) in events"
            :key="event.id"
            class="timeline-item"
            :class="{ 'timeline-item-last': index === events.length - 1 }"
          >
            <div class="timeline-marker" :class="getEventMarkerClass(event.type)">
              <i :class="getEventIcon(event.type)"></i>
            </div>
            <div class="timeline-content">
              <div class="timeline-header">
                <h6 class="timeline-title">{{ getEventTitle(event.type) }}</h6>
                <small class="timeline-time text-muted">
                  {{ formatDateTime(event.createdAt) }}
                </small>
              </div>
              <div class="timeline-body">
                <p class="mb-2">{{ event.description }}</p>
                
                <!-- 事件详细信息 -->
                <div v-if="event.details" class="event-details">
                  <div
                    v-for="(value, key) in event.details"
                    :key="key"
                    class="detail-item"
                  >
                    <span class="detail-label">{{ getDetailLabel(key) }}:</span>
                    <span class="detail-value">{{ formatDetailValue(key, value) }}</span>
                  </div>
                </div>

                <!-- 区块链信息 -->
                <div v-if="event.transactionHash" class="blockchain-info mt-2">
                  <small class="text-muted">
                    <i class="bi bi-link-45deg"></i>
                    交易哈希: 
                    <a
                      :href="getBlockchainExplorerUrl(event.transactionHash)"
                      target="_blank"
                      class="text-decoration-none"
                    >
                      {{ formatHash(event.transactionHash) }}
                      <i class="bi bi-box-arrow-up-right ms-1"></i>
                    </a>
                  </small>
                </div>

                <!-- 操作者信息 -->
                <div v-if="event.operatorAddress" class="operator-info mt-2">
                  <small class="text-muted">
                    <i class="bi bi-person"></i>
                    操作者: {{ formatAddress(event.operatorAddress) }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 区块链验证说明 -->
        <div class="alert alert-info mt-4">
          <div class="d-flex align-items-start">
            <i class="bi bi-shield-check me-2 mt-1"></i>
            <div>
              <h6 class="alert-heading mb-1">区块链验证</h6>
              <p class="mb-0">
                所有专利事件均记录在区块链上，确保数据的不可篡改性和可追溯性。
                您可以点击交易哈希查看区块链浏览器中的详细信息。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import patentService from '@/services/patentService'

export default {
  name: 'PatentEvents',
  props: {
    patentId: {
      type: [String, Number],
      required: true,
    },
  },
  emits: ['events-loaded'],
  setup(props, { emit }) {
    // 响应式数据
    const isLoading = ref(true)
    const error = ref('')
    const events = ref([])

    // 组件挂载时加载事件
    onMounted(() => {
      loadEvents()
    })

    // 加载事件历史
    const loadEvents = async () => {
      try {
        isLoading.value = true
        error.value = ''

        const result = await patentService.getPatentEvents(props.patentId)
        events.value = result.events || []

        // 发出事件加载完成的信号
        emit('events-loaded', events.value)

      } catch (err) {
        console.error('加载专利事件失败:', err)
        error.value = err.message || '加载失败，请稍后重试'
      } finally {
        isLoading.value = false
      }
    }

    // 获取事件标记样式
    const getEventMarkerClass = (eventType) => {
      const classMap = {
        CREATED: 'timeline-marker-success',
        APPROVED: 'timeline-marker-success',
        REJECTED: 'timeline-marker-danger',
        TRADE_INITIATED: 'timeline-marker-info',
        TRADE_COMPLETED: 'timeline-marker-success',
        TRADE_CANCELLED: 'timeline-marker-warning',
        FROZEN: 'timeline-marker-dark',
        UNFROZEN: 'timeline-marker-success',
        REVOKED: 'timeline-marker-danger',
        RIGHTS_CLAIMED: 'timeline-marker-warning',
        RIGHTS_APPROVED: 'timeline-marker-success',
        RIGHTS_REJECTED: 'timeline-marker-danger',
      }
      return classMap[eventType] || 'timeline-marker-secondary'
    }

    // 获取事件图标
    const getEventIcon = (eventType) => {
      const iconMap = {
        CREATED: 'bi bi-plus-circle',
        APPROVED: 'bi bi-check-circle',
        REJECTED: 'bi bi-x-circle',
        TRADE_INITIATED: 'bi bi-arrow-left-right',
        TRADE_COMPLETED: 'bi bi-check2-circle',
        TRADE_CANCELLED: 'bi bi-x-circle',
        FROZEN: 'bi bi-pause-circle',
        UNFROZEN: 'bi bi-play-circle',
        REVOKED: 'bi bi-trash',
        RIGHTS_CLAIMED: 'bi bi-shield-exclamation',
        RIGHTS_APPROVED: 'bi bi-shield-check',
        RIGHTS_REJECTED: 'bi bi-shield-x',
      }
      return iconMap[eventType] || 'bi bi-circle'
    }

    // 获取事件标题
    const getEventTitle = (eventType) => {
      const titleMap = {
        CREATED: '专利创建',
        APPROVED: '审核通过',
        REJECTED: '审核拒绝',
        TRADE_INITIATED: '交易发起',
        TRADE_COMPLETED: '交易完成',
        TRADE_CANCELLED: '交易取消',
        FROZEN: '专利冻结',
        UNFROZEN: '专利解冻',
        REVOKED: '专利撤销',
        RIGHTS_CLAIMED: '维权申请',
        RIGHTS_APPROVED: '维权成功',
        RIGHTS_REJECTED: '维权失败',
      }
      return titleMap[eventType] || eventType
    }

    // 获取详情标签
    const getDetailLabel = (key) => {
      const labelMap = {
        price: '价格',
        buyer: '买方',
        seller: '卖方',
        reason: '原因',
        claimant: '申请人',
        evidence: '证据',
        auditor: '审核员',
      }
      return labelMap[key] || key
    }

    // 格式化详情值
    const formatDetailValue = (key, value) => {
      if (key === 'price') {
        return `${value} ETH`
      }
      if (key === 'buyer' || key === 'seller' || key === 'claimant' || key === 'auditor') {
        return formatAddress(value)
      }
      return value
    }

    // 格式化日期时间
    const formatDateTime = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    // 格式化地址
    const formatAddress = (address) => {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    // 格式化哈希
    const formatHash = (hash) => {
      if (!hash) return ''
      return `${hash.slice(0, 8)}...${hash.slice(-8)}`
    }

    // 获取区块链浏览器URL
    const getBlockchainExplorerUrl = (transactionHash) => {
      // 这里应该根据实际的区块链网络返回相应的浏览器URL
      // 例如以太坊主网: https://etherscan.io/tx/${transactionHash}
      // Ganache本地网络可能没有浏览器，返回一个占位符
      return `#/transaction/${transactionHash}`
    }

    return {
      isLoading,
      error,
      events,
      loadEvents,
      getEventMarkerClass,
      getEventIcon,
      getEventTitle,
      getDetailLabel,
      formatDetailValue,
      formatDateTime,
      formatAddress,
      formatHash,
      getBlockchainExplorerUrl,
    }
  },
}
</script>

<style scoped>
.patent-events .card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-header h5 {
  color: #495057;
  font-weight: 600;
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
}

.timeline-item-last {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: #fff;
  z-index: 1;
}

.timeline-marker-success {
  background-color: #198754;
}

.timeline-marker-danger {
  background-color: #dc3545;
}

.timeline-marker-info {
  background-color: #0dcaf0;
}

.timeline-marker-warning {
  background-color: #ffc107;
  color: #000;
}

.timeline-marker-dark {
  background-color: #212529;
}

.timeline-marker-secondary {
  background-color: #6c757d;
}

.timeline-content {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 1rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.timeline-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.timeline-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0;
}

.timeline-time {
  margin-left: auto;
  white-space: nowrap;
}

.timeline-body p {
  color: #6c757d;
  margin-bottom: 0.5rem;
}

.event-details {
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  padding: 0.75rem;
  margin-top: 0.5rem;
}

.detail-item {
  display: flex;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #495057;
  min-width: 4rem;
}

.detail-value {
  color: #6c757d;
  margin-left: 0.5rem;
}

.blockchain-info,
.operator-info {
  font-size: 0.875rem;
}

.blockchain-info a {
  color: #0d6efd;
}

.blockchain-info a:hover {
  color: #0a58ca;
}

.alert-info {
  background-color: #cff4fc;
  border-color: #b6effb;
  color: #055160;
}

.alert-heading {
  color: inherit;
  font-weight: 600;
}

@media (max-width: 768px) {
  .timeline {
    padding-left: 1.5rem;
  }
  
  .timeline::before {
    left: 0.75rem;
  }
  
  .timeline-marker {
    left: -1.5rem;
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }
  
  .timeline-content {
    padding: 0.75rem;
  }
  
  .timeline-header {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .timeline-time {
    margin-left: 0;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 0.125rem;
  }
  
  .detail-label {
    min-width: auto;
  }
  
  .detail-value {
    margin-left: 0;
  }
}
</style>
