<template>
  <div class="network-validator">
    <!-- Correct Network -->
    <div v-if="metamaskStore.isGanacheNetwork" class="text-success small">
      <i class="bi bi-check-circle-fill me-1"></i>
      <span>正确网络</span>
    </div>
    
    <!-- Wrong Network Warning -->
    <div v-else-if="metamaskStore.isConnected" class="d-flex align-items-center">
      <div class="text-warning small me-2">
        <i class="bi bi-exclamation-triangle-fill me-1"></i>
        <span>请切换到 Ganache 网络</span>
      </div>
      
      <button 
        @click="switchToGanache"
        class="btn btn-warning btn-sm"
        :disabled="isSwitching"
      >
        <span v-if="isSwitching" class="spinner-border spinner-border-sm me-1" role="status">
          <span class="visually-hidden">切换中...</span>
        </span>
        <i v-else class="bi bi-arrow-repeat me-1"></i>
        {{ isSwitching ? '切换中...' : '切换网络' }}
      </button>
    </div>

    <!-- Network Setup Guide Modal -->
    <div 
      class="modal fade" 
      id="networkGuideModal" 
      tabindex="-1" 
      ref="networkGuideModal"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-gear me-2"></i>
              Ganache 网络配置指南
            </h5>
            <button 
              type="button" 
              class="btn-close" 
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <h6>什么是 Ganache？</h6>
                <p class="text-muted">
                  Ganache 是一个本地以太坊区块链开发工具，用于测试和开发区块链应用程序。
                </p>
                
                <h6>网络配置信息</h6>
                <div class="table-responsive">
                  <table class="table table-sm">
                    <tbody>
                      <tr>
                        <td><strong>网络名称:</strong></td>
                        <td>Ganache Local</td>
                      </tr>
                      <tr>
                        <td><strong>RPC URL:</strong></td>
                        <td><code>http://localhost:7545</code></td>
                      </tr>
                      <tr>
                        <td><strong>链 ID:</strong></td>
                        <td>1337</td>
                      </tr>
                      <tr>
                        <td><strong>货币符号:</strong></td>
                        <td>ETH</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div class="col-md-6">
                <h6>配置步骤</h6>
                <ol class="list-group list-group-numbered">
                  <li class="list-group-item d-flex justify-content-between align-items-start border-0 px-0">
                    <div class="ms-2 me-auto">
                      <div class="fw-bold">启动 Ganache</div>
                      确保 Ganache 应用程序正在运行
                    </div>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-start border-0 px-0">
                    <div class="ms-2 me-auto">
                      <div class="fw-bold">打开 MetaMask</div>
                      点击网络选择器
                    </div>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-start border-0 px-0">
                    <div class="ms-2 me-auto">
                      <div class="fw-bold">添加网络</div>
                      选择"添加网络"并输入配置信息
                    </div>
                  </li>
                  <li class="list-group-item d-flex justify-content-between align-items-start border-0 px-0">
                    <div class="ms-2 me-auto">
                      <div class="fw-bold">切换网络</div>
                      选择新添加的 Ganache 网络
                    </div>
                  </li>
                </ol>
              </div>
            </div>
            
            <div class="alert alert-info mt-3">
              <i class="bi bi-info-circle me-2"></i>
              <strong>提示：</strong>
              您也可以点击上方的"切换网络"按钮，系统将自动为您添加和切换到 Ganache 网络。
            </div>
          </div>
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              data-bs-dismiss="modal"
            >
              关闭
            </button>
            <button 
              @click="switchToGanache"
              class="btn btn-primary"
              :disabled="isSwitching"
            >
              <span v-if="isSwitching" class="spinner-border spinner-border-sm me-1" role="status">
                <span class="visually-hidden">切换中...</span>
              </span>
              <i v-else class="bi bi-arrow-repeat me-1"></i>
              {{ isSwitching ? '切换中...' : '自动切换网络' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useMetaMaskStore } from '@/stores/metamask'
import { Modal } from 'bootstrap'

export default {
  name: 'NetworkValidator',
  setup() {
    const metamaskStore = useMetaMaskStore()
    const isSwitching = ref(false)
    const networkGuideModal = ref(null)

    // Switch to Ganache network
    async function switchToGanache() {
      isSwitching.value = true
      
      try {
        const success = await metamaskStore.switchToGanache()
        
        if (success) {
          // Close modal if open
          if (networkGuideModal.value) {
            const modal = Modal.getInstance(networkGuideModal.value)
            if (modal) {
              modal.hide()
            }
          }
        } else {
          // Show setup guide if failed
          showNetworkGuide()
        }
      } catch (error) {
        console.error('切换网络失败:', error)
        showNetworkGuide()
      } finally {
        isSwitching.value = false
      }
    }

    // Show network setup guide
    function showNetworkGuide() {
      if (networkGuideModal.value) {
        const modal = new Modal(networkGuideModal.value)
        modal.show()
      }
    }

    return {
      metamaskStore,
      isSwitching,
      networkGuideModal,
      switchToGanache,
      showNetworkGuide
    }
  }
}
</script>

<style scoped>
.network-validator {
  display: inline-block;
}

.table-responsive {
  border-radius: 0.375rem;
  overflow: hidden;
}

.table {
  margin-bottom: 0;
}

.table td {
  padding: 0.375rem 0.75rem;
  border-bottom: 1px solid #dee2e6;
}

.table tr:last-child td {
  border-bottom: none;
}

code {
  font-size: 0.875em;
  background-color: #f8f9fa;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
}

.list-group-numbered {
  counter-reset: section;
}

.list-group-numbered .list-group-item::before {
  content: counter(section);
  counter-increment: section;
  background-color: #0d6efd;
  color: white;
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 50%;
  font-size: 0.875rem;
  margin-right: 0.5rem;
  min-width: 1.5rem;
  text-align: center;
  display: inline-block;
}
</style> 