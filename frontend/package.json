{"name": "blockchain-patent-exchange", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"bootstrap": "^5.3.6", "bootstrap-icons": "^1.13.1", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "web3": "^4.16.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "prettier": "3.5.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}